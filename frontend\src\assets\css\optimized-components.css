/* ==================== 优化的组件样式系统 ==================== */

/* ==================== 现代化卡片组件 ==================== */
.modern-card {
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: transform 0.15s ease, box-shadow 0.15s ease;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.modern-card-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(248, 250, 252, 0.5);
}

.modern-card-body {
  padding: 1.5rem;
}

.modern-card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(248, 250, 252, 0.3);
}

/* ==================== 优化的按钮系统 ==================== */
.btn-modern {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  border: none;
  cursor: pointer;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-modern-primary {
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
  color: white;
}

.btn-modern-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.btn-modern-secondary {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-modern-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.btn-modern-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

.btn-modern-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* ==================== 现代化表格 ==================== */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
}

.modern-table th {
  background: #f8fafc;
  padding: 1rem 0.75rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  border-bottom: 1px solid #e5e7eb;
}

.modern-table td {
  padding: 0.875rem 0.75rem;
  border-bottom: 1px solid #f3f4f6;
  color: #6b7280;
  font-size: 0.875rem;
}

.modern-table tr:hover {
  background: #f9fafb;
}

.modern-table tr:last-child td {
  border-bottom: none;
}

/* ==================== 优化的表单控件 ==================== */
.modern-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: border-color 0.15s ease, box-shadow 0.15s ease;
  background: white;
}

.modern-input:focus {
  outline: none;
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.modern-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.15s ease, box-shadow 0.15s ease;
}

.modern-select:focus {
  outline: none;
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

/* ==================== 状态徽章 ==================== */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge-success {
  background: #dcfce7;
  color: #166534;
}

.status-badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.status-badge-danger {
  background: #fee2e2;
  color: #991b1b;
}

.status-badge-info {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge-secondary {
  background: #f1f5f9;
  color: #475569;
}

/* ==================== 加载状态 ==================== */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.loading-spinner-modern {
  width: 2rem;
  height: 2rem;
  border: 3px solid #f3f4f6;
  border-radius: 50%;
  border-top-color: #1e40af;
  animation: spin 0.8s linear infinite;
  margin-bottom: 1rem;
}

/* ==================== 空状态 ==================== */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
}

.empty-state-icon {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 1rem;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.empty-state-description {
  font-size: 0.875rem;
  color: #6b7280;
}

/* ==================== 分页组件 ==================== */
.modern-pagination {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-pagination-item {
  padding: 0.5rem 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: white;
  color: #374151;
  text-decoration: none;
  font-size: 0.875rem;
  transition: all 0.15s ease;
  cursor: pointer;
}

.modern-pagination-item:hover {
  background: #f9fafb;
  border-color: #d1d5db;
}

.modern-pagination-item.active {
  background: #1e40af;
  border-color: #1e40af;
  color: white;
}

.modern-pagination-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ==================== 工具提示 ==================== */
.tooltip-modern {
  position: relative;
  cursor: help;
}

.tooltip-modern::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.15s ease;
  z-index: 1000;
  margin-bottom: 0.5rem;
}

.tooltip-modern:hover::after {
  opacity: 1;
  visibility: visible;
}

/* ==================== 响应式优化 ==================== */
@media (max-width: 768px) {
  .modern-card-header,
  .modern-card-body,
  .modern-card-footer {
    padding: 1rem;
  }
  
  .modern-table th,
  .modern-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.8rem;
  }
  
  .btn-modern {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }
  
  .modern-pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}
