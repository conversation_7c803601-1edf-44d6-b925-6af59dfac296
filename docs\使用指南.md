# 运维文档管理平台 - 使用指南

## 📋 目录

1. [平台概述](#平台概述)
2. [快速开始](#快速开始)
3. [功能模块](#功能模块)
4. [权限管理](#权限管理)
5. [故障排除](#故障排除)
6. [常见问题](#常见问题)

## 🎯 平台概述

运维文档管理平台是一个专业的运维文档生成和管理系统，帮助运维团队高效管理服务器配置、应用部署和系统监控信息。

### 主要功能

- **表单管理** - 创建和管理运维文档表单
- **组件管理** - 管理服务器组件和配置
- **模板管理** - 自定义Excel导出模板
- **用户权限** - 基于角色的权限管理
- **数据导入** - 支持Excel和JSON数据导入
- **性能监控** - 实时系统性能监控

### 系统架构

```
前端界面 (Vue.js + Bootstrap)
    ↓
后端服务 (Python Flask)
    ↓
数据存储 (MySQL + Redis)
```

### 部署架构

#### 生产环境
- **Web服务器**: Nginx (反向代理)
- **应用服务**: Flask (端口5000)
- **数据库**: MySQL 8.0.42
- **缓存**: Redis (DB 0)
- **服务器**: 172.16.59.18

#### 开发环境
- **前端开发**: Vue Dev Server (8080)
- **后端开发**: Flask Dev (5000)
- **开发数据库**: export_excel_dev
- **开发缓存**: Redis (DB 1)
- **包管理**: uv (Python)

### 技术栈详情

| 层级 | 技术 | 版本/配置 | 说明 |
|------|------|----------|------|
| 前端 | Vue.js + Bootstrap | Vue 3 + Bootstrap 5 | 响应式用户界面 |
| 后端 | Python Flask | Flask + SQLAlchemy | RESTful API服务 |
| 数据库 | MySQL | 8.0.42 | 主数据存储 |
| 缓存 | Redis | 6.x | 会话和数据缓存 |
| 代理 | Nginx | 反向代理 | 负载均衡和静态文件 |

## 🚀 快速开始

### 默认管理员账户

- **用户名**: admin
- **密码**: admin123

> ⚠️ 首次登录后请及时修改密码

### 创建第一个表单

1. **选择表单类型** - 在首页选择"应用加固"或"安全监控"表单类型
2. **填写基本信息** - 输入客户公司名称、项目名称等基础信息
3. **配置服务器信息** - 添加服务器IP、组件配置等技术信息
4. **生成文档** - 点击"生成表单"按钮导出Excel运维文档

### 使用技巧

✅ **提示**
- 使用表单快照功能保存草稿
- 组件信息可以重复使用
- 支持批量导入Excel数据

⚠️ **注意**
- 密码字段默认隐藏，可点击查看
- 必填字段标有红色星号
- 表单提交前会检查重复

## 📊 功能模块

### 表单管理

| 表单类型 | 用途 | 主要字段 | 生成文档 |
|---------|------|----------|----------|
| 应用加固 | 移动应用安全加固项目 | 客户信息、平台版本、服务器配置 | 应用加固运维文档 |
| 安全监测 | 安全监测平台部署 | 前后端版本、访问地址、组件配置 | 安全监测运维文档 |
| 安全测评 | 移动应用安全测评项目 | 部署包版本、管理员信息、服务器配置 | 安全测评运维文档 |

#### 表单操作

- **新建表单** - 创建新的运维文档表单
- **编辑表单** - 修改已有表单内容
- **导出文档** - 生成Excel运维文档

#### 表单快照

表单快照可以保存当前填写状态，方便下次继续编辑：

- **自动保存** - 系统会定期自动保存表单状态
- **手动保存** - 点击"保存草稿"按钮手动保存
- **加载快照** - 在表单页面可以选择加载之前的快照
- **快照管理** - 可以删除不需要的快照释放空间

### 组件管理

组件是服务器上部署的软件或服务，如Web服务器、数据库、监控工具等。

#### 如何使用组件管理

**使用步骤：**
1. **添加组件** - 在组件管理页面添加常用的软件组件
2. **设置信息** - 配置组件名称、默认端口、分类等信息
3. **表单选择** - 在填写表单时，从组件列表中选择需要的组件
4. **自动填充** - 系统会自动填充组件的端口和配置信息
5. **生成文档** - 在Excel文档中自动生成部署架构图

**组件管理功能：**
- **统一管理** - 集中管理所有常用组件信息
- **快速复用** - 在表单中快速选择和复用组件
- **自动配置** - 自动填充端口号和默认配置
- **架构图生成** - 自动生成部署架构图表

#### 组件分类

- **Web服务** - Nginx, Apache, Tomcat
- **数据库** - MySQL, Redis, MongoDB
- **安全组件** - 防火墙, WAF, IDS
- **监控工具** - Zabbix, Prometheus

#### 组件操作

| 操作 | 说明 | 权限要求 |
|------|------|----------|
| 新建组件 | 添加新的组件类型和配置 | component.create |
| 编辑组件 | 修改组件名称、端口、描述等信息 | component.edit |
| 启用/禁用 | 控制组件是否在表单中可选 | component.edit |
| 删除组件 | 永久删除组件（谨慎操作） | component.delete |

### 模板管理

Excel模板定义了运维文档的格式和样式，支持自定义模板来满足不同项目需求。

#### 模板功能

- **模板上传** - 支持上传自定义Excel模板文件
- **版本管理** - 支持多个模板版本，可以动态切换

#### 模板占位符

模板使用双花括号占位符处理动态内容：

**基本字段占位符：**
```
{{公司名称}}
{{记录日期}}
{{部署包版本}}
{{管理员账号}}
```

**组件占位符：**
```
{{docker.port}}
{{nginx.version}}
{{mysql.count}}
{{组件名.字段}}
```

**动态列表占位符：**
```
{{服务器信息 | range}}
{{维护记录 | range}}
```

**复合占位符：**
```
{{运维信息}}
{{客户APP}}
```

### 系统管理

#### 性能监控

实时监控系统性能，包括：

- **CPU监控** - 实时CPU使用率
- **内存监控** - 内存使用情况
- **磁盘监控** - 磁盘空间使用
- **性能统计** - API响应时间

#### 文件管理

- **批量操作** - 支持批量选择和删除历史文件
- **分页显示** - 支持10、20、50条数据的分页显示
- **文件备份** - 重要文件自动备份到专用目录
- **编辑历史** - 记录文件编辑操作和修改内容

#### 缓存管理

系统使用多级缓存提升性能：

- 用户权限缓存
- 表单数据缓存
- 组件信息缓存
- 系统配置缓存

## 🔐 权限管理

系统采用基于角色的权限管理（RBAC），支持用户、角色、权限的灵活配置。

### 默认角色

| 角色 | 权限范围 | 适用人员 |
|------|----------|----------|
| 管理员 | 所有权限 | 系统管理员 |
| 运维人员 | 表单、组件、缓存管理 | 运维工程师 |
| 表单管理员 | 表单和模板管理 | 文档管理员 |
| 查看者 | 只读权限 | 普通用户 |

### 权限模块

- **用户管理** - user.*
- **角色管理** - role.*
- **权限管理** - permission.*
- **表单管理** - form.*
- **组件管理** - component.*
- **模板管理** - template.*
- **系统管理** - system.*
- **缓存管理** - cache.*
- **历史记录** - history.*

## 🔌 API接口

系统提供完整的RESTful API接口，支持第三方系统集成和自动化操作。

### 接口分类

| 模块 | 接口数量 | 主要功能 | 权限要求 |
|------|----------|----------|----------|
| 认证管理 | 7个 | 登录、注册、token验证 | 无需权限 |
| 表单管理 | 15+个 | 表单CRUD、导入导出 | form.* |
| 组件管理 | 5个 | 组件CRUD操作 | component.* |
| 权限管理 | 12+个 | 用户、角色、权限管理 | user.*, role.* |
| 性能监控 | 12个 | 系统监控、统计报告 | system.view |

### 认证方式

所有API接口使用JWT Token进行认证：

1. 通过 `/auth/login` 接口获取access_token
2. 在请求头中添加: `Authorization: Bearer {token}`
3. Token有效期为24小时，过期需重新登录

### 在线调试

访问 **API文档** 页面可以：

- 查看完整的接口文档
- 在线测试API接口
- 查看请求和响应示例
- 根据权限显示可用接口

## 🛠️ 故障排除

### 登录失败或权限不足

**可能原因：**
- 用户名或密码错误
- 用户账户被禁用
- Token过期或无效
- 用户权限不足

**解决方案：**
1. 检查用户名和密码是否正确
2. 联系管理员检查账户状态
3. 清除浏览器缓存重新登录
4. 联系管理员分配相应权限

### 表单提交失败

**可能原因：**
- 必填字段未填写
- 数据格式不正确
- 网络连接问题
- 服务器错误

**解决方案：**
1. 检查所有必填字段（红色星号标记）
2. 验证邮箱、电话等格式是否正确
3. 检查网络连接状态
4. 稍后重试或检查系统状态

### Excel导出异常

**可能原因：**
- 模板文件损坏
- 数据包含特殊字符
- 服务器磁盘空间不足
- Excel模板版本不兼容

**解决方案：**
1. 重新上传Excel模板
2. 检查数据中的特殊字符
3. 联系管理员检查服务器状态
4. 使用默认模板重新生成

### 系统性能问题

**性能优化建议：**
- **清理缓存** - 定期清理系统缓存释放内存
- **数据清理** - 删除不需要的历史记录和文件
- **浏览器优化** - 清除浏览器缓存和Cookie
- **网络检查** - 确保网络连接稳定

**监控指标：**
- CPU使用率应低于80%
- 内存使用率应低于85%
- 磁盘使用率应低于90%
- API响应时间应低于2秒

## ❓ 常见问题

### Q: 如何修改密码？
A: 点击右上角用户头像 → 个人资料 → 修改密码，输入当前密码和新密码即可。

### Q: 如何批量导入数据？
A: 在表单页面点击"导入数据"按钮，支持Excel和JSON格式的批量数据导入。

### Q: 表单数据能否恢复？
A: 系统会自动备份重要数据，删除的表单可以在一定时间内通过管理员恢复。

### Q: 如何自定义模板？
A: 在模板管理页面上传自定义Excel模板，使用Jinja2语法定义动态内容。

### Q: 支持哪些浏览器？
A: 推荐使用Chrome、Firefox、Edge等现代浏览器，IE浏览器可能存在兼容性问题。

### Q: 数据安全如何保障？
A: 系统采用JWT认证、权限控制、数据加密等多重安全措施保护用户数据。



## 📈 更新日志

### v2.1.0 (2024-12-06)
- 新增性能监控功能
- 优化权限管理体系
- 增强API文档和在线调试
- 改进用户界面和体验

### v2.0.0 (2024-11-15)
- 重构权限管理系统
- 新增表单快照功能
- 优化Excel模板引擎
- 增强数据导入导出

### v1.5.0 (2024-10-20)
- 新增组件管理功能
- 支持多模板版本管理
- 优化表单验证机制
- 改进系统性能

---

*本文档最后更新时间：2024年12月6日*
