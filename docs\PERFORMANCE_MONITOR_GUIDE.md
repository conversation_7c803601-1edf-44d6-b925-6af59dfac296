# 📊 性能监控页面使用指南

## 🎯 功能概述

性能监控页面提供了系统运行状态的实时监控和分析，帮助管理员了解系统性能并进行优化。

## 🚀 访问方式

### 1. 导航菜单访问
- 登录系统后，点击顶部导航栏的 **"系统管理"** 下拉菜单
- 选择 **"性能监控"** 选项
- 或直接访问：`http://localhost:8080/performance-monitor`

### 2. 权限要求
- 需要 `system.view` 权限
- 管理员默认拥有所有权限

## 📋 页面功能

### 🏥 系统健康状态
显示系统整体健康状况：
- **健康 (healthy)** - 系统运行正常 🟢
- **警告 (warning)** - 存在性能问题 🟡  
- **严重 (critical)** - 系统存在严重问题 🔴

### 💻 系统资源监控
实时显示服务器资源使用情况：

#### CPU 使用率
- 显示当前CPU使用百分比
- 进度条可视化显示
- 超过80%会触发警告

#### 内存使用率  
- 显示当前内存使用百分比
- 超过85%会触发警告

#### 磁盘使用率
- 显示磁盘空间使用百分比
- 超过90%会触发严重警告

### ⚡ 缓存性能统计
监控Redis缓存的性能表现：

#### 总体统计
- **总体命中率**: 所有缓存类型的平均命中率
- **缓存命中**: 成功从缓存获取数据的次数
- **缓存未命中**: 需要从数据库获取数据的次数
- **总请求数**: 缓存请求总数

#### 分类统计
显示各种缓存类型的详细统计：
- `user_permissions` - 用户权限缓存
- `components` - 组件配置缓存
- `templates` - 模板配置缓存
- `form_config` - 表单字段配置缓存
- `user_info` - 用户信息缓存
- 等等...

#### 命中率颜色说明
- 🟢 **绿色**: 命中率 ≥ 80% (优秀)
- 🟡 **黄色**: 命中率 60-80% (良好)
- 🔴 **红色**: 命中率 < 60% (需要优化)

### 📈 请求性能统计
监控API接口的性能表现：

#### 时间范围选择
- 最近1小时 (默认)
- 最近6小时
- 最近24小时

#### 统计指标
- **请求次数**: 接口被调用的总次数
- **平均响应时间**: 接口平均处理时间 (毫秒)
- **最大响应时间**: 最慢的一次请求时间
- **最小响应时间**: 最快的一次请求时间
- **错误率**: 返回4xx/5xx状态码的请求比例

### 🐌 慢查询监控
显示执行时间超过1秒的数据库查询：

#### 显示信息
- **时间**: 查询执行的时间
- **执行时间**: 查询耗时 (秒)
- **SQL语句**: 执行的SQL语句 (截取前200字符)

#### 优化建议
- 如果出现慢查询，建议检查：
  - 是否缺少索引
  - 查询条件是否合理
  - 数据量是否过大

### 💡 性能优化建议
系统自动分析性能数据并提供优化建议：

#### 建议类型
- **系统 (system)**: CPU、内存、磁盘相关建议
- **缓存 (cache)**: 缓存策略优化建议
- **数据库 (database)**: 数据库性能优化建议
- **网络 (network)**: 网络相关优化建议

#### 优先级
- 🔴 **高优先级 (high)**: 需要立即处理
- 🟡 **中优先级 (medium)**: 建议尽快处理
- 🔵 **低优先级 (low)**: 可以稍后处理

## 🔄 操作功能

### 刷新数据
- 点击右上角 **"刷新数据"** 按钮
- 手动更新所有监控数据
- 页面每30秒自动刷新一次

### 清理数据
- 点击右上角 **"清理数据"** 按钮
- 清除历史性能监控数据
- 需要确认操作，不可恢复

## 📊 性能指标解读

### 🎯 健康的系统指标
- CPU使用率: < 70%
- 内存使用率: < 80%
- 磁盘使用率: < 90%
- 缓存命中率: > 80%
- API响应时间: < 200ms
- 错误率: < 1%

### ⚠️ 需要关注的指标
- CPU使用率: 70-80%
- 内存使用率: 80-85%
- 磁盘使用率: 90-95%
- 缓存命中率: 60-80%
- API响应时间: 200-500ms
- 错误率: 1-5%

### 🚨 严重问题指标
- CPU使用率: > 80%
- 内存使用率: > 85%
- 磁盘使用率: > 95%
- 缓存命中率: < 60%
- API响应时间: > 500ms
- 错误率: > 5%

## 🔧 常见问题处理

### 1. 页面加载失败
**可能原因**:
- 权限不足
- 后端服务未启动
- 网络连接问题

**解决方案**:
- 检查用户权限
- 确认后端服务运行状态
- 检查网络连接

### 2. 数据显示异常
**可能原因**:
- 性能监控服务未启动
- 数据库连接问题
- Redis连接问题

**解决方案**:
- 重启后端应用
- 检查数据库连接
- 检查Redis服务状态

### 3. 缓存命中率低
**可能原因**:
- 缓存时间设置过短
- 缓存键设计不合理
- 数据变化频繁

**解决方案**:
- 调整缓存时间配置
- 优化缓存策略
- 分析数据访问模式

## 📞 技术支持

如果遇到问题，请联系：
- **技术支持**: <EMAIL>
- **系统管理员**: <EMAIL>

## 📚 相关文档

- [性能优化分析报告](PERFORMANCE_OPTIMIZATION_ANALYSIS.md)
- [性能优化总结](PERFORMANCE_OPTIMIZATION_SUMMARY.md)
- [数据库表结构说明](DATABASE_TABLES_OVERVIEW.md)

---

> 📝 **创建时间**: 2024-12-19  
> 🔄 **更新时间**: 2024-12-19  
> 👤 **开发者**: Augment Agent  
> 📋 **状态**: 可使用
