<template>
  <collapsible-card card-class="border-success" storage-key="server-info-table-section">
    <template #header>
      <span>
        <i class="bi bi-server me-2"></i>服务器信息
      </span>
      <!-- 右上角切换按钮 -->
      <div class="server-view-controls-header" @click.stop>
        <div class="btn-group btn-group-sm" role="group">
          <input
            id="server-view-card-table-header"
            :checked="viewMode === 'card'"
            type="radio"
            value="card"
            class="btn-check"
            @change.stop="$emit('update:view-mode', 'card')"
          />
          <label for="server-view-card-table-header" class="btn btn-outline-primary" @click.stop>
            <i class="bi bi-card-list me-1"></i>卡片
          </label>

          <input
            id="server-view-table-table-header"
            :checked="viewMode === 'table'"
            type="radio"
            value="table"
            class="btn-check"
            @change.stop="$emit('update:view-mode', 'table')"
          />
          <label for="server-view-table-table-header" class="btn btn-outline-primary" @click.stop>
            <i class="bi bi-table me-1"></i>表格
          </label>
        </div>
      </div>
    </template>
    <template #summary>
      <div class="d-flex flex-wrap gap-2">
        <span class="badge bg-success">服务器数量: {{ serverList.length }}</span>
        <span v-for="(item, index) in serverList" :key="'server-summary-' + index" class="badge bg-light text-dark border">
          {{ item.用途类型 === '自定义' ? item.用途 : item.用途类型 }}
          {{ item.IP地址 ? '(' + item.IP地址 + ')' : '' }}
        </span>
      </div>
    </template>

    <div v-if="serverList.length === 0" class="text-center py-3 text-muted">
      <i class="bi bi-info-circle me-2"></i>暂无服务器信息
      <div class="mt-2">
        <button type="button" class="btn btn-success btn-sm" @click="addServerItem">
          <i class="bi bi-plus-circle me-1"></i>添加第一台服务器
        </button>
      </div>
    </div>

    <!-- 表格形式的服务器信息 -->
    <div v-else class="table-responsive">
      <table class="table table-bordered table-hover server-table">
        <thead class="table-success">
          <tr>
            <th style="width: 60px;">#</th>
            <th style="width: 120px;">用途类型 <span class="text-danger">*</span></th>
            <th style="width: 150px;">自定义用途</th>
            <th style="width: 140px;">IP地址 <span class="text-danger">*</span></th>
            <th style="width: 120px;">外网IP</th>
            <th style="width: 120px;">系统发行版</th>
            <th style="width: 100px;">系统架构</th>
            <th style="width: 100px;">内存</th>
            <th style="width: 100px;">CPU</th>
            <th style="width: 100px;">磁盘</th>
            <th style="width: 200px;">部署应用</th>
            <th style="width: 80px;">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(server, index) in serverList" :key="'server-row-' + index" class="server-row">
            <!-- 序号 -->
            <td class="text-center align-middle">
              <span class="badge bg-primary">{{ index + 1 }}</span>
            </td>
            
            <!-- 用途类型 -->
            <td>
              <select v-model="server.用途类型" class="form-select form-select-sm" @change="onPurposeTypeChange(server)">
                <option value="DMZ">DMZ</option>
                <option value="内网">内网</option>
                <option value="自定义">自定义</option>
              </select>
            </td>
            
            <!-- 自定义用途 -->
            <td>
              <input 
                v-model="server.用途" 
                type="text" 
                class="form-control form-control-sm" 
                placeholder="请输入用途"
                :disabled="server.用途类型 !== '自定义'"
              />
            </td>
            
            <!-- IP地址 -->
            <td>
              <input
                v-model="server.IP地址"
                type="text"
                class="form-control form-control-sm"
                placeholder="*************"
                @input="onIpChange"
              />
            </td>
            
            <!-- 外网IP -->
            <td>
              <input 
                v-model="server.外网IP地址" 
                type="text" 
                class="form-control form-control-sm" 
                placeholder="可选"
              />
            </td>
            
            <!-- 系统发行版 -->
            <td>
              <input
                v-model="server.系统发行版"
                type="text"
                class="form-control form-control-sm"
                placeholder="请输入或选择"
                :list="`os-list-${index}`"
              />
              <datalist :id="`os-list-${index}`">
                <option value="CentOS 7"></option>
                <option value="CentOS 8"></option>
                <option value="Ubuntu 18.04"></option>
                <option value="Ubuntu 20.04"></option>
                <option value="Ubuntu 22.04"></option>
                <option value="Red Hat 7"></option>
                <option value="Red Hat 8"></option>
                <option value="Red Hat 9"></option>
                <option value="Debian 10"></option>
                <option value="Debian 11"></option>
                <option value="Debian 12"></option>
                <option value="Rocky Linux 8"></option>
                <option value="Rocky Linux 9"></option>
                <option value="AlmaLinux 8"></option>
                <option value="AlmaLinux 9"></option>
                <option value="SUSE Linux"></option>
                <option value="openSUSE"></option>
              </datalist>
            </td>

            <!-- 系统架构 -->
            <td>
              <input
                v-model="server.系统架构"
                type="text"
                class="form-control form-control-sm"
                placeholder="请输入或选择"
                :list="`arch-list-${index}`"
              />
              <datalist :id="`arch-list-${index}`">
                <option value="x86_64"></option>
                <option value="aarch64"></option>
                <option value="arm64"></option>
                <option value="i386"></option>
                <option value="i686"></option>
                <option value="armv7l"></option>
                <option value="s390x"></option>
                <option value="ppc64le"></option>
              </datalist>
            </td>

            <!-- 内存 -->
            <td>
              <input
                v-model="server.内存"
                type="text"
                class="form-control form-control-sm"
                placeholder="请输入或选择"
                :list="`memory-list-${index}`"
              />
              <datalist :id="`memory-list-${index}`">
                <option value="1GB"></option>
                <option value="2GB"></option>
                <option value="4GB"></option>
                <option value="8GB"></option>
                <option value="16GB"></option>
                <option value="32GB"></option>
                <option value="64GB"></option>
                <option value="128GB"></option>
                <option value="256GB"></option>
                <option value="512GB"></option>
              </datalist>
            </td>

            <!-- CPU -->
            <td>
              <input
                v-model="server.CPU"
                type="text"
                class="form-control form-control-sm"
                placeholder="请输入或选择"
                :list="`cpu-list-${index}`"
              />
              <datalist :id="`cpu-list-${index}`">
                <option value="1核"></option>
                <option value="2核"></option>
                <option value="4核"></option>
                <option value="8核"></option>
                <option value="16核"></option>
                <option value="32核"></option>
                <option value="64核"></option>
                <option value="1核2线程"></option>
                <option value="2核4线程"></option>
                <option value="4核8线程"></option>
                <option value="8核16线程"></option>
                <option value="16核32线程"></option>
              </datalist>
            </td>

            <!-- 磁盘 -->
            <td>
              <input
                v-model="server.磁盘"
                type="text"
                class="form-control form-control-sm"
                placeholder="请输入或选择"
                :list="`disk-list-${index}`"
              />
              <datalist :id="`disk-list-${index}`">
                <option value="20GB"></option>
                <option value="40GB"></option>
                <option value="80GB"></option>
                <option value="100GB"></option>
                <option value="200GB"></option>
                <option value="500GB"></option>
                <option value="1TB"></option>
                <option value="2TB"></option>
                <option value="5TB"></option>
                <option value="10TB"></option>
                <option value="20TB"></option>
                <option value="100GB SSD"></option>
                <option value="500GB SSD"></option>
                <option value="1TB SSD"></option>
                <option value="2TB SSD"></option>
              </datalist>
            </td>
            
            <!-- 部署应用 -->
            <td>
              <button
                type="button"
                class="btn btn-outline-primary btn-sm w-100"
                :ref="`componentBtn-${index}`"
                @click="showComponentModal(index, $event)"
              >
                <i class="bi bi-gear me-1"></i>
                {{ server.部署应用?.length ? `已选${server.部署应用.length}个` : '选择应用' }}
              </button>
            </td>
            
            <!-- 操作 -->
            <td class="text-center">
              <div class="btn-group-vertical btn-group-sm">
                <button
                  type="button"
                  class="btn btn-outline-info btn-sm"
                  :ref="`advancedBtn-${index}`"
                  @click="showAdvancedModal(index, $event)"
                  title="高级设置"
                >
                  <i class="bi bi-gear"></i>
                </button>
                <button 
                  type="button" 
                  class="btn btn-outline-danger btn-sm" 
                  @click="removeServerItem(index)"
                  title="删除服务器"
                >
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 添加按钮 -->
    <div v-if="serverList.length > 0" class="add-button-section mt-3">
      <button type="button" class="btn btn-success" @click="addServerItem">
        <i class="bi bi-plus-circle me-2"></i>
        <span>添加服务器</span>
      </button>
    </div>

    <!-- 组件选择模态框 -->
    <component-selection-modal
      v-if="showComponentModalFlag"
      :server-index="currentServerIndex"
      :server-info="serverList[currentServerIndex]"
      :document-type="documentType"
      :component-groups="componentGroups"
      :trigger-element="currentTriggerElement"
      @close="closeComponentModal"
      @update="updateServerComponents"
    />

    <!-- 高级设置模态框 -->
    <advanced-settings-modal
      v-if="showAdvancedModalFlag"
      :server-index="currentServerIndex"
      :server-info="serverList[currentServerIndex]"
      :trigger-element="currentTriggerElement"
      @close="closeAdvancedModal"
      @update="updateServerAdvanced"
    />
  </collapsible-card>
</template>

<script>
import CollapsibleCard from './CollapsibleCard.vue'
import ComponentSelectionModal from './ComponentSelectionModal.vue'
import AdvancedSettingsModal from './AdvancedSettingsModal.vue'
import { saveFormDataToLocalStorage, loadFormDataFromLocalStorage } from '@/utils/fillSheetData'
import { createNewServerItem } from '@/config/formDataConfig'

export default {
  name: 'ServerInfoTable',
  components: {
    CollapsibleCard,
    ComponentSelectionModal,
    AdvancedSettingsModal
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    },
    documentType: {
      type: String,
      required: true
    },
    componentGroups: {
      type: Object,
      default: () => ({})
    },
    // 显示模式
    viewMode: {
      type: String,
      default: 'table'
    }
  },
  emits: ['update:modelValue', 'refresh-components', 'show-toast', 'update:view-mode'],
  data() {
    return {
      showComponentModalFlag: false,
      showAdvancedModalFlag: false,
      currentServerIndex: -1,
      currentTriggerElement: null
    }
  },
  computed: {
    serverList: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  methods: {
    addServerItem() {
      const newServer = createNewServerItem()
      this.serverList.push(newServer)
      this.saveData()
      // 移除 refresh-components 事件，添加服务器不需要刷新组件数据
      // this.$emit('refresh-components')
    },
    
    removeServerItem(index) {
      if (confirm('确定要删除这台服务器吗？')) {
        this.serverList.splice(index, 1)
        this.saveData()
        // 移除 refresh-components 事件，删除服务器不需要刷新组件数据
        // this.$emit('refresh-components')
      }
    },
    
    onPurposeTypeChange(server) {
      if (server.用途类型 !== '自定义') {
        server.用途 = ''
      }
      this.saveData()
    },
    
    onIpChange() {
      this.saveData()
      // 移除 refresh-components 事件，IP变化不需要刷新组件数据
      // this.$emit('refresh-components')
    },
    
    showComponentModal(index, event) {
      this.currentServerIndex = index
      this.currentTriggerElement = event.target
      this.showComponentModalFlag = true
    },

    closeComponentModal() {
      this.showComponentModalFlag = false
      this.currentServerIndex = -1
      this.currentTriggerElement = null
    },
    
    updateServerComponents(data) {
      if (this.currentServerIndex >= 0) {
        const server = this.serverList[this.currentServerIndex]
        server.部署应用 = data.components
        server.组件端口 = data.ports
        this.saveData()
        // 移除 refresh-components 事件，选择应用不需要刷新组件数据
        // this.$emit('refresh-components')
      }
    },
    
    showAdvancedModal(index, event) {
      this.currentServerIndex = index
      this.currentTriggerElement = event.target
      this.showAdvancedModalFlag = true
    },

    closeAdvancedModal() {
      this.showAdvancedModalFlag = false
      this.currentServerIndex = -1
      this.currentTriggerElement = null
    },
    
    updateServerAdvanced(data) {
      if (this.currentServerIndex >= 0) {
        const server = this.serverList[this.currentServerIndex]
        Object.assign(server, data)
        this.saveData()
      }
    },
    
    saveData() {
      // 不再直接保存到localStorage，避免覆盖其他表单数据
      // 服务器信息的保存应该通过父组件的v-model机制自动处理
      console.log('ServerInfoTable: 服务器数据已更新，通过v-model自动同步')
    }
  }
}
</script>

<style scoped>
.server-table {
  font-size: 0.875rem;
}

.server-table th {
  background-color: #d1e7dd;
  font-weight: 600;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
}

.server-table td {
  vertical-align: middle;
  padding: 0.5rem 0.25rem;
}

.server-row:hover {
  background-color: #f8f9fa;
}

.form-control-sm, .form-select-sm {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
}

.btn-group-vertical .btn {
  margin-bottom: 2px;
}

.btn-group-vertical .btn:last-child {
  margin-bottom: 0;
}

.add-button-section {
  text-align: center;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.table-responsive {
  max-height: 600px;
  overflow-y: auto;
}

/* 固定表头 */
.table-responsive .table thead th {
  position: sticky;
  top: 0;
  z-index: 10;
}
</style>
