"""
分享链接相关路由
"""
import uuid
import traceback
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, current_app
from app.models.models import FormSubmission
from app.models.auth_models import User
from app.auth.decorators import permission_required, get_current_user
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
import hashlib
import json

share_bp = Blueprint('share', __name__, url_prefix='/api/share')


class ShareLink:
    """分享链接管理类"""
    
    @staticmethod
    def generate_share_token(submission_id, sharer_id, expires_hours=72):
        """生成分享令牌"""
        # 生成唯一的分享ID
        share_id = str(uuid.uuid4())

        # 计算过期时间 - 支持永久链接
        if expires_hours == 0 or expires_hours is None:
            # 永久链接：设置为100年后过期
            expires_at = datetime.utcnow() + timedelta(days=365 * 100)
        else:
            expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
        
        # 创建分享数据
        share_data = {
            'share_id': share_id,
            'submission_id': submission_id,
            'sharer_id': sharer_id,
            'created_at': datetime.utcnow().isoformat(),
            'expires_at': expires_at.isoformat(),
            'access_count': 0
        }
        
        # 存储到Redis缓存中
        try:
            from app.utils.cache_utils import cache
            cache_key = f"share_link:{share_id}"

            # 设置缓存超时时间
            if expires_hours == 0 or expires_hours is None:
                # 永久链接：设置较长的缓存时间（30天）
                cache_timeout = 30 * 24 * 3600  # 30天
            else:
                cache_timeout = expires_hours * 3600

            cache.set(cache_key, json.dumps(share_data), timeout=cache_timeout)
            current_app.logger.info(f"分享数据已缓存: {cache_key}, 超时: {cache_timeout}秒")
        except Exception as cache_error:
            current_app.logger.error(f"缓存分享数据失败: {str(cache_error)}")
            # 缓存失败不影响功能，继续执行
        
        return share_id, expires_at
    
    @staticmethod
    def get_share_data(share_id):
        """获取分享数据"""
        try:
            from app.utils.cache_utils import cache
            cache_key = f"share_link:{share_id}"

            share_data_str = cache.get(cache_key)
            if not share_data_str:
                current_app.logger.info(f"分享数据不存在: {cache_key}")
                return None

            share_data = json.loads(share_data_str)

            # 检查是否过期
            expires_at = datetime.fromisoformat(share_data['expires_at'])
            if datetime.utcnow() > expires_at:
                current_app.logger.info(f"分享链接已过期: {share_id}")
                cache.delete(cache_key)
                return None

            current_app.logger.info(f"获取分享数据成功: {share_id}")
            return share_data
        except Exception as e:
            current_app.logger.error(f"获取分享数据失败: {str(e)}")
            return None
    
    @staticmethod
    def increment_access_count(share_id):
        """增加访问次数"""
        try:
            from app.utils.cache_utils import cache
            cache_key = f"share_link:{share_id}"

            share_data_str = cache.get(cache_key)
            if share_data_str:
                share_data = json.loads(share_data_str)
                share_data['access_count'] += 1

                # 计算剩余过期时间
                expires_at = datetime.fromisoformat(share_data['expires_at'])
                remaining_seconds = int((expires_at - datetime.utcnow()).total_seconds())

                if remaining_seconds > 0:
                    cache.set(cache_key, json.dumps(share_data), timeout=remaining_seconds)
                    current_app.logger.info(f"访问次数已更新: {share_id}, count: {share_data['access_count']}")

        except Exception as e:
            current_app.logger.error(f"更新访问次数失败: {str(e)}")


@share_bp.route('/create', methods=['POST'])
def create_share_link():
    """
    创建分享链接
    """
    try:
        current_app.logger.info("=== 开始创建分享链接 ===")

        # 检查Authorization头
        auth_header = request.headers.get('Authorization')
        current_app.logger.info(f"Authorization头: {auth_header}")

        if auth_header:
            if auth_header.startswith('Bearer '):
                token = auth_header[7:]  # 去掉 "Bearer " 前缀
                current_app.logger.info(f"提取的token: {token[:50]}...")
                current_app.logger.info(f"Token长度: {len(token)}")
                current_app.logger.info(f"Token分段数: {len(token.split('.'))}")
            else:
                current_app.logger.error("Authorization头格式错误，不是Bearer token")
        else:
            current_app.logger.error("缺少Authorization头")

        # 手动验证JWT token
        from flask_jwt_extended import verify_jwt_in_request
        try:
            verify_jwt_in_request()
            current_app.logger.info("JWT验证成功")
        except Exception as jwt_error:
            current_app.logger.error(f"JWT验证失败: {str(jwt_error)}")
            current_app.logger.error(f"JWT错误类型: {type(jwt_error).__name__}")
            return jsonify({
                'status': 'error',
                'message': '认证失败，请重新登录',
                'error_detail': str(jwt_error),
                'error_type': type(jwt_error).__name__
            }), 401

        # 获取当前用户ID
        user_id = get_jwt_identity()
        current_app.logger.info(f"当前用户ID: {user_id}")

        if not user_id:
            current_app.logger.error("用户未登录")
            return jsonify({
                'status': 'error',
                'message': '用户未登录'
            }), 401

        # 获取用户对象
        current_user = User.query.get(int(user_id))
        current_app.logger.info(f"当前用户: {current_user}")

        if not current_user or not current_user.is_active:
            current_app.logger.error("用户不存在或已被禁用")
            return jsonify({
                'status': 'error',
                'message': '用户不存在或已被禁用'
            }), 401

        # 检查权限
        if not current_user.has_permission('form.view'):
            current_app.logger.error("用户权限不足")
            return jsonify({
                'status': 'error',
                'message': '权限不足，无法创建分享链接'
            }), 403

        data = request.get_json()
        current_app.logger.info(f"请求数据: {data}")

        if not data:
            current_app.logger.error("请求数据为空")
            return jsonify({
                'status': 'error',
                'message': '请求数据不能为空'
            }), 400

        submission_id = data.get('submission_id')
        expires_hours = data.get('expires_hours', 72)  # 默认72小时

        current_app.logger.info(f"提交记录ID: {submission_id}, 过期小时: {expires_hours}")

        if not submission_id:
            current_app.logger.error("提交记录ID为空")
            return jsonify({
                'status': 'error',
                'message': '提交记录ID不能为空'
            }), 400
        
        # 验证提交记录是否存在
        submission = FormSubmission.query.get(submission_id)
        if not submission:
            return jsonify({
                'status': 'error',
                'message': '提交记录不存在'
            }), 404
        
        # 生成分享链接
        share_id, expires_at = ShareLink.generate_share_token(
            submission_id, 
            current_user.id, 
            expires_hours
        )
        
        # 构建分享URL - 根据环境配置
        # 获取请求来源，判断是开发环境还是生产环境
        origin = request.headers.get('Origin', '')
        referer = request.headers.get('Referer', '')
        host = request.headers.get('Host', '')

        current_app.logger.info(f"请求头信息 - Origin: {origin}, Referer: {referer}, Host: {host}")

        # 判断环境并构建前端URL
        if 'localhost' in origin or '127.0.0.1' in origin or 'localhost' in referer or '127.0.0.1' in referer:
            # 开发环境：前端运行在独立端口
            if 'localhost:8080' in origin or 'localhost:8080' in referer:
                frontend_base = 'http://localhost:8080'
            elif '127.0.0.1:8080' in origin or '127.0.0.1:8080' in referer:
                frontend_base = 'http://127.0.0.1:8080'
            else:
                # 默认开发环境地址
                frontend_base = 'http://localhost:8080'
        else:
            # 生产环境：检查是否有端口号
            if origin:
                frontend_base = origin
            elif referer:
                # 从referer中提取base URL
                from urllib.parse import urlparse
                parsed = urlparse(referer)
                frontend_base = f"{parsed.scheme}://{parsed.netloc}"
            else:
                # 回退方案：使用Host头构建URL
                scheme = 'https' if request.is_secure else 'http'
                frontend_base = f"{scheme}://{host}"

        share_url = f"{frontend_base}/share/{share_id}"
        current_app.logger.info(f"生成的分享URL: {share_url}")
        
        return jsonify({
            'status': 'success',
            'message': '分享链接创建成功',
            'data': {
                'share_id': share_id,
                'share_url': share_url,
                'expires_at': expires_at.isoformat(),
                'expires_hours': expires_hours,
                'sharer': {
                    'id': current_user.id,
                    'username': current_user.username,
                    'real_name': getattr(current_user, 'real_name', current_user.username)
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"创建分享链接失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'创建分享链接失败: {str(e)}',
            'error_type': type(e).__name__,
            'traceback': traceback.format_exc()
        }), 500


@share_bp.route('/<share_id>', methods=['GET'])
def get_shared_submission(share_id):
    """
    通过分享链接获取表单详情（无需登录）
    """
    try:
        # 获取分享数据
        share_data = ShareLink.get_share_data(share_id)
        if not share_data:
            return jsonify({
                'status': 'error',
                'message': '分享链接无效或已过期'
            }), 404
        
        # 增加访问次数
        ShareLink.increment_access_count(share_id)
        
        # 获取提交记录
        submission = FormSubmission.query.get(share_data['submission_id'])
        if not submission:
            return jsonify({
                'status': 'error',
                'message': '提交记录不存在'
            }), 404
        
        # 获取分享者信息
        sharer = User.query.get(share_data['sharer_id'])
        sharer_info = {
            'username': sharer.username if sharer else '未知用户',
            'real_name': getattr(sharer, 'real_name', sharer.username) if sharer else '未知用户'
        }
        
        return jsonify({
            'status': 'success',
            'data': {
                'submission': submission.to_dict(include_form_data=True),
                'share_info': {
                    'share_id': share_id,
                    'sharer': sharer_info,
                    'created_at': share_data['created_at'],
                    'expires_at': share_data['expires_at'],
                    'access_count': share_data['access_count']
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分享内容失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取分享内容失败: {str(e)}'
        }), 500


@share_bp.route('/info/<share_id>', methods=['GET'])
def get_share_info(share_id):
    """
    获取分享链接信息（无需登录）
    """
    try:
        share_data = ShareLink.get_share_data(share_id)
        if not share_data:
            return jsonify({
                'status': 'error',
                'message': '分享链接无效或已过期'
            }), 404
        
        # 获取基本信息（不包含详细数据）
        submission = FormSubmission.query.get(share_data['submission_id'])
        if not submission:
            return jsonify({
                'status': 'error',
                'message': '提交记录不存在'
            }), 404
        
        # 获取分享者信息
        sharer = User.query.get(share_data['sharer_id'])
        sharer_info = {
            'username': sharer.username if sharer else '未知用户',
            'real_name': getattr(sharer, 'real_name', sharer.username) if sharer else '未知用户'
        }
        
        return jsonify({
            'status': 'success',
            'data': {
                'submission_info': {
                    'id': submission.id,
                    'company_name': submission.company_name,
                    'form_type': submission.form_type,
                    'created_at': submission.created_at.isoformat(),
                    'updated_at': submission.updated_at.isoformat()
                },
                'share_info': {
                    'share_id': share_id,
                    'sharer': sharer_info,
                    'created_at': share_data['created_at'],
                    'expires_at': share_data['expires_at'],
                    'access_count': share_data['access_count']
                }
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取分享信息失败: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return jsonify({
            'status': 'error',
            'message': f'获取分享信息失败: {str(e)}'
        }), 500
