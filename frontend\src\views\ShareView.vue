<template>
  <div class="share-view">
    <!-- 加载状态 -->
    <div v-if="loading" class="d-flex justify-content-center align-items-center" style="min-height: 50vh;">
      <div class="text-center">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-3 text-muted">正在加载分享内容...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-6">
          <div class="card border-danger">
            <div class="card-body text-center">
              <i class="bi bi-exclamation-triangle text-danger" style="font-size: 3rem;"></i>
              <h4 class="card-title mt-3 text-danger">访问失败</h4>
              <p class="card-text">{{ error }}</p>
              <a href="/" class="btn btn-primary">返回首页</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分享内容 -->
    <div v-else-if="shareData" class="container mt-4">
      <!-- 分享信息头部 -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card bg-light">
            <div class="card-body">
              <div class="row align-items-center">
                <div class="col-md-8">
                  <h5 class="card-title mb-1">
            <i class="bi bi-share me-2"></i>
            {{ shareData.submission?.company_name || '未知公司' }} - {{ shareData.submission?.form_type || '未知类型' }}
          </h5>
                  <small class="text-muted">
                    <i class="bi bi-person me-1"></i>
                    由 {{ shareData.share_info.sharer.real_name }} 分享
                    <span class="mx-2">|</span>
                    <i class="bi bi-calendar me-1"></i>
                    {{ formatDate(shareData.submission.created_at) }}
                  </small>
                </div>
                <div class="col-md-4 text-end">
                  <small class="text-muted">
                    <i class="bi bi-eye me-1"></i>
                    访问次数：{{ shareData.share_info.access_count }}
                  </small>
                  <br>
                  <small class="text-muted">
                    <i class="bi bi-clock me-1"></i>
                    过期时间：{{ formatDate(shareData.share_info.expires_at) }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单详情内容 -->
      <div class="row">
        <div class="col-12">
          <submission-detail-modal
            v-if="shareData.submission"
            :submission="shareData.submission"
            :is-share-mode="true"
            @close="() => {}"
          />
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="row mt-4">
        <div class="col-12">
          <div class="card border-info">
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <h6 class="text-info">
                    <i class="bi bi-info-circle me-2"></i>
                    关于此分享
                  </h6>
                  <p class="small text-muted mb-0">
                    此内容由 {{ shareData.share_info.sharer.real_name }} 通过梆梆安全运维信息登记平台分享。
                    分享链接将在 {{ formatDate(shareData.share_info.expires_at) }} 过期。
                  </p>
                </div>
                <div class="col-md-6 text-end">
                  <a href="/" class="btn btn-outline-primary btn-sm">
                    <i class="bi bi-house me-1"></i>
                    访问完整平台
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SubmissionDetailModal from '@/components/modals/SubmissionDetailModal.vue'

const API_BASE_URL = process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000'

export default {
  name: 'ShareView',
  components: {
    SubmissionDetailModal
  },
  data() {
    return {
      loading: true,
      error: null,
      shareData: null
    }
  },
  async mounted() {
    await this.loadShareData()
  },
  methods: {
    async loadShareData() {
      try {
        const shareId = this.$route.params.shareId
        if (!shareId) {
          this.error = '无效的分享链接'
          this.loading = false
          return
        }

        const response = await fetch(`${API_BASE_URL}/api/share/${shareId}`)
        const result = await response.json()

        if (result.status === 'success') {
          this.shareData = result.data
          
          // 设置页面标题
          const companyName = this.shareData.submission?.company_name || '未知公司'
          const formType = this.shareData.submission?.form_type || '未知类型'
          document.title = `${companyName} - ${formType} | 梆梆安全`
        } else {
          this.error = result.message || '获取分享内容失败'
        }
      } catch (error) {
        console.error('加载分享数据失败:', error)
        this.error = '网络错误，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.share-view {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}
</style>
