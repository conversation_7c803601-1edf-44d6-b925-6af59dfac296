<template>
  <div class="container-fluid py-4">
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
              <i class="bi bi-magic"></i>
              自动填充修改功能测试
            </h5>
            <small>测试用户修改自动填充内容后的行为</small>
          </div>
          <div class="card-body">
            
            <!-- 表单类型选择 -->
            <div class="row mb-4">
              <div class="col-md-4">
                <label class="form-label">选择表单类型</label>
                <select v-model="selectedFormType" @change="loadFormType" class="form-select">
                  <option value="">请选择表单类型</option>
                  <option value="安全测评">安全测评</option>
                  <option value="安全监测">安全监测</option>
                  <option value="应用加固">应用加固</option>
                </select>
              </div>
              <div class="col-md-8">
                <label class="form-label">操作</label>
                <div class="btn-group d-block">
                  <button @click="simulateServerListChange" class="btn btn-success btn-sm me-2">
                    <i class="bi bi-arrow-clockwise"></i> 模拟服务器列表变化
                  </button>
                  <button @click="clearAll" class="btn btn-warning btn-sm me-2">
                    <i class="bi bi-trash"></i> 清空重置
                  </button>
                  <button @click="addLog('手动触发测试', 'info')" class="btn btn-info btn-sm">
                    <i class="bi bi-play"></i> 手动测试
                  </button>
                </div>
              </div>
            </div>

            <!-- 测试服务器列表 -->
            <div class="row mb-4" v-if="selectedFormType">
              <div class="col-12">
                <div class="card bg-light">
                  <div class="card-header">
                    <h6 class="mb-0">测试服务器列表</h6>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-sm">
                        <thead>
                          <tr>
                            <th>IP地址</th>
                            <th>部署应用</th>
                            <th>组件端口</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(server, index) in testServerList" :key="index">
                            <td>{{ server.IP地址 }}</td>
                            <td>{{ Array.isArray(server.部署应用) ? server.部署应用.join(', ') : server.部署应用 }}</td>
                            <td>
                              <small v-for="(port, component) in server.组件端口" :key="component">
                                {{ component }}: {{ port }}<br>
                              </small>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 动态表单渲染 -->
            <div class="row" v-if="selectedFormType">
              <div class="col-md-8">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">{{ selectedFormType }}表单 - 访问信息部分</h6>
                  </div>
                  <div class="card-body">
                    <!-- 安全测评表单 -->
                    <SecurityTestingAccessInfo
                      v-if="selectedFormType === '安全测评'"
                      :key="refreshKey"
                      :server-list="testServerList"
                      :admin-page="testFormData.管理员页面IP || ''"
                      :user-page="testFormData.用户页面IP || ''"
                      :upgrade-page="testFormData.升级页面IP || ''"
                      :external-service="testFormData.对外服务端口 || ''"
                      @update:adminPage="updateField('管理员页面IP', $event)"
                      @update:userPage="updateField('用户页面IP', $event)"
                      @update:upgradePage="updateField('升级页面IP', $event)"
                      @update:externalService="updateField('对外服务端口', $event)"
                    />
                    
                    <!-- 安全监测表单 -->
                    <SecurityMonitoringAccessInfo
                      v-if="selectedFormType === '安全监测'"
                      :key="refreshKey"
                      :server-list="testServerList"
                      :business-page="testFormData.业务功能页面地址 || ''"
                      :init-address="testFormData.init地址 || ''"
                      :kibana-address="testFormData.kibana地址 || ''"
                      @update:businessPage="updateField('业务功能页面地址', $event)"
                      @update:initAddress="updateField('init地址', $event)"
                      @update:kibanaAddress="updateField('kibana地址', $event)"
                    />
                    
                    <!-- 应用加固表单 -->
                    <AppHardeningAccessInfo
                      v-if="selectedFormType === '应用加固'"
                      :key="refreshKey"
                      :server-list="testServerList"
                      :platform-access-url="testFormData.平台访问地址 || ''"
                      :upgrade-platform-url="testFormData.升级平台地址 || ''"
                      @update:platformAccessUrl="updateField('平台访问地址', $event)"
                      @update:upgradePlatformUrl="updateField('升级平台地址', $event)"
                    />
                  </div>
                </div>
              </div>
              
              <!-- 调试日志 -->
              <div class="col-md-4">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">调试日志</h6>
                    <button @click="debugLogs = []" class="btn btn-sm btn-outline-secondary">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                  <div class="card-body p-2" style="max-height: 400px; overflow-y: auto;">
                    <div v-for="(log, index) in debugLogs" :key="index" 
                         :class="['alert', 'alert-' + log.type, 'py-1', 'px-2', 'mb-1', 'small']">
                      <small class="text-muted">{{ log.time }}</small><br>
                      {{ log.message }}
                    </div>
                    <div v-if="debugLogs.length === 0" class="text-muted text-center">
                      暂无日志
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 当前表单数据展示 -->
            <div class="row mt-4" v-if="selectedFormType">
              <div class="col-12">
                <div class="card">
                  <div class="card-header">
                    <h6 class="mb-0">当前表单数据</h6>
                  </div>
                  <div class="card-body">
                    <pre class="bg-light p-3 rounded">{{ JSON.stringify(testFormData, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getInitialFormData } from '@/config/formDataConfig'
import SecurityTestingAccessInfo from '@/components/forms/securityTesting/AccessInfoSection.vue'
import SecurityMonitoringAccessInfo from '@/components/forms/securityMonitoring/AccessInfoSection.vue'
import AppHardeningAccessInfo from '@/components/forms/appHardening/AccessInfoSection.vue'

export default {
  name: 'AutoFillModificationTest',
  components: {
    SecurityTestingAccessInfo,
    SecurityMonitoringAccessInfo,
    AppHardeningAccessInfo
  },
  data() {
    return {
      selectedFormType: '',
      testFormData: {},
      refreshKey: 0,
      debugLogs: [],
      
      // 测试服务器列表
      testServerList: [
        {
          IP地址: '*************',
          部署应用: ['front-ssp-admin', 'front-ssp-user'],
          组件端口: {
            'front-ssp-admin': '8080',
            'front-ssp-user': '8081'
          }
        },
        {
          IP地址: '*************',
          部署应用: ['backend-ssp-user', 'luna'],
          组件端口: {
            'backend-ssp-user': '9120',
            'luna': '8080'
          }
        },
        {
          IP地址: '*************',
          部署应用: ['web-service-nginx', 'init', 'kibana'],
          组件端口: {
            'web-service-nginx': '443',
            'init': '8181',
            'kibana': '5601'
          }
        },
        {
          IP地址: '*************',
          部署应用: ['secweb'],
          组件端口: {
            'secweb': '8000'
          }
        }
      ]
    }
  },
  methods: {
    /**
     * 加载表单类型
     */
    loadFormType() {
      if (!this.selectedFormType) return

      this.addLog(`加载表单类型: ${this.selectedFormType}`, 'info')

      // 获取初始表单数据
      this.testFormData = getInitialFormData(this.selectedFormType)

      // 强制刷新组件
      this.refreshKey += 1

      this.addLog('表单类型加载完成', 'success')
    },

    /**
     * 更新字段值
     */
    updateField(fieldName, value) {
      this.testFormData[fieldName] = value
      this.addLog(`字段更新: ${fieldName} = "${value}"`, 'info')
    },

    /**
     * 模拟服务器列表变化
     */
    simulateServerListChange() {
      // 随机修改一个IP地址来触发变化
      const randomIndex = Math.floor(Math.random() * this.testServerList.length)
      const randomIP = `192.168.1.${100 + Math.floor(Math.random() * 50)}`
      
      this.testServerList[randomIndex].IP地址 = randomIP
      this.addLog(`模拟服务器列表变化: 服务器${randomIndex + 1} IP改为 ${randomIP}`, 'warning')
      
      // 强制触发响应式更新
      this.testServerList = [...this.testServerList]
    },

    /**
     * 清空重置
     */
    clearAll() {
      this.testFormData = getInitialFormData(this.selectedFormType)
      this.debugLogs = []
      this.refreshKey += 1
      this.addLog('数据已清空重置', 'info')
    },

    /**
     * 添加调试日志
     */
    addLog(message, type = 'info') {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.debugLogs.push({
        time,
        message,
        type
      })
      
      // 限制日志数量
      if (this.debugLogs.length > 50) {
        this.debugLogs = this.debugLogs.slice(-50)
      }
    }
  }
}
</script>

<style scoped>
.alert {
  font-size: 0.875rem;
}

pre {
  font-size: 0.8rem;
  max-height: 300px;
  overflow-y: auto;
}

.table-sm td, .table-sm th {
  padding: 0.25rem;
  font-size: 0.875rem;
}
</style>
