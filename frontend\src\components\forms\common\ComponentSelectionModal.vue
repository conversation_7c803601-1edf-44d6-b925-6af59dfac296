<template>
  <div class="modal-backdrop fade show" @click="$emit('close')"></div>
  <div class="modal-container" :style="modalStyle">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">
            <i class="bi bi-gear me-2"></i>
            服务器 #{{ serverIndex + 1 }} - 部署应用配置
          </h5>
          <button type="button" class="btn-close" @click="$emit('close')" aria-label="关闭">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <!-- 组件选择表格 -->
          <div class="mb-4">
            <h6 class="mb-3">
              <i class="bi bi-list-check me-2"></i>选择部署应用
            </h6>
            <div v-if="formattedComponentGroups.length === 0" class="text-muted text-center py-3">
              <i class="bi bi-info-circle me-2"></i>暂无可用组件
              <div class="small mt-2">
                <div>文档类型: {{ documentType }}</div>
                <div>组件分组数据: {{ Object.keys(componentGroups).length > 0 ? '已加载' : '未加载' }}</div>
                <div v-if="Object.keys(componentGroups).length > 0">
                  可用表单类型: {{ Object.keys(componentGroups).join(', ') }}
                </div>
                <div>formTypeKey: {{ getFormTypeKey(documentType) }}</div>
                <div v-if="componentGroups[getFormTypeKey(documentType)]">
                  当前类型分组: {{ Object.keys(componentGroups[getFormTypeKey(documentType)]).join(', ') }}
                </div>
              </div>
            </div>
            <div v-else>
              <!-- 表格形式的组件选择 -->
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead class="table-light">
                    <tr>
                      <th width="60">
                        <input
                          id="selectAll"
                          v-model="selectAllChecked"
                          type="checkbox"
                          class="form-check-input"
                          @change="toggleSelectAll"
                          title="全选/取消全选"
                        />
                        <label for="selectAll" class="form-check-label ms-1">全选</label>
                      </th>
                      <th>组件名称</th>
                      <th>分类</th>
                      <th width="120">端口</th>
                      <th>描述</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="component in allComponents" :key="component.name"
                        :class="{ 'table-success': selectedComponents.includes(component.name) }">
                      <td>
                        <input
                          :id="`component-${serverIndex}-${component.name}`"
                          v-model="selectedComponents"
                          :value="component.name"
                          type="checkbox"
                          class="form-check-input"
                          @change="onComponentSelectionChange(component)"
                        />
                      </td>
                      <td>
                        <label :for="`component-${serverIndex}-${component.name}`" class="form-check-label fw-medium">
                          {{ component.displayName || component.display_name || component.name }}
                        </label>
                      </td>
                      <td>
                        <span class="badge" :class="getCategoryBadgeClass(component.category)">
                          {{ getCategoryDisplayName(component.category) }}
                        </span>
                      </td>
                      <td>
                        <input
                          v-if="selectedComponents.includes(component.name)"
                          v-model="componentPorts[component.name]"
                          type="text"
                          class="form-control form-control-sm"
                          :placeholder="component.port || '8080'"
                          @input="onPortChange(component.name, $event.target.value)"
                        />
                        <span v-else class="text-muted">{{ component.port || '8080' }}</span>
                      </td>
                      <td>
                        <small class="text-muted">{{ component.description || component.desc || '-' }}</small>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>


        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" @click="$emit('close')">
            取消
          </button>
          <button type="button" class="btn btn-primary" @click="saveConfiguration">
            <i class="bi bi-check-lg me-1"></i>
            保存配置
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getComponentDefaultPort } from '@/utils/componentUtils'

export default {
  name: 'ComponentSelectionModal',
  props: {
    serverIndex: {
      type: Number,
      required: true
    },
    serverInfo: {
      type: Object,
      required: true
    },
    documentType: {
      type: String,
      required: true
    },
    componentGroups: {
      type: Object,
      default: () => ({})
    },
    triggerElement: {
      type: Object,
      default: null
    }
  },
  emits: ['close', 'update'],
  computed: {
    // 将对象格式的组件分组转换为数组格式
    formattedComponentGroups() {
      console.log('🔍 ComponentSelectionModal - formattedComponentGroups 被调用')
      console.log('📦 componentGroups keys:', Object.keys(this.componentGroups))
      console.log('📋 documentType:', this.documentType)

      if (!this.componentGroups || typeof this.componentGroups !== 'object') {
        console.log('❌ componentGroups 为空或不是对象')
        return []
      }

      // 根据文档类型获取对应的组件分组
      const formTypeKey = this.getFormTypeKey(this.documentType)
      console.log('🔑 formTypeKey:', formTypeKey)

      const typeGroups = this.componentGroups[formTypeKey]
      console.log('📂 typeGroups:', typeGroups)

      if (!typeGroups || typeof typeGroups !== 'object') {
        console.log('❌ typeGroups 为空或不是对象')
        return []
      }

      // 转换为数组格式
      const result = Object.keys(typeGroups).map(groupKey => {
        const components = typeGroups[groupKey] || []
        console.log(`🔍 分组 ${groupKey} 的组件:`, components)

        // 检查组件数据结构
        if (components.length > 0) {
          console.log(`📋 ${groupKey} 第一个组件的结构:`, components[0])
        }

        return {
          name: groupKey,
          displayName: this.getGroupDisplayName(groupKey),
          components: components
        }
      })

      console.log('✅ 转换后的结果:', result)
      return result
    },

    // 将所有组件展平为一维数组，用于表格显示
    allComponents() {
      const components = []
      this.formattedComponentGroups.forEach(group => {
        group.components.forEach(component => {
          components.push({
            ...component,
            category: group.name,
            categoryDisplayName: group.displayName
          })
        })
      })
      return components
    },

    // 全选状态计算
    selectAllChecked: {
      get() {
        return this.allComponents.length > 0 &&
               this.selectedComponents.length === this.allComponents.length
      },
      set(value) {
        // 这个setter主要用于v-model，实际逻辑在toggleSelectAll中
      }
    },

    // 是否为半选状态（部分选中）
    isIndeterminate() {
      return this.selectedComponents.length > 0 &&
             this.selectedComponents.length < this.allComponents.length
    }
  },
  watch: {
    // 监听componentGroups变化
    componentGroups: {
      handler(newGroups, oldGroups) {
        console.log('🔄 ComponentSelectionModal: componentGroups变化')
        console.log('📦 新数据:', newGroups)
        console.log('📦 旧数据:', oldGroups)
      },
      deep: true,
      immediate: true
    },

    // 监听选中组件变化，更新全选状态
    selectedComponents: {
      handler() {
        this.updateSelectAllState()
      },
      immediate: true
    }
  },
  data() {
    return {
      selectedComponents: [],
      componentPorts: {},
      modalStyle: {}
    }
  },
  mounted() {
    console.log('🚀 ComponentSelectionModal mounted')
    console.log('📦 Props componentGroups:', this.componentGroups)
    console.log('📦 componentGroups类型:', typeof this.componentGroups)
    console.log('📦 componentGroups键:', Object.keys(this.componentGroups))
    console.log('📋 Props documentType:', this.documentType)
    console.log('🔧 Props serverInfo:', this.serverInfo)

    // 立即检查formattedComponentGroups
    console.log('🔄 立即检查formattedComponentGroups:', this.formattedComponentGroups)

    this.initializeData()
    this.calculateModalPosition()

    // 监听窗口大小变化
    this.resizeHandler = () => {
      this.calculateModalPosition()
    }
    window.addEventListener('resize', this.resizeHandler)
  },

  beforeUnmount() {
    // 清理事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }
  },
  methods: {
    initializeData() {
      // 初始化已选择的组件
      this.selectedComponents = [...(this.serverInfo.部署应用 || [])]

      // 初始化端口配置
      this.componentPorts = { ...(this.serverInfo.组件端口 || {}) }

      // 为已选择的组件设置默认端口（如果没有配置）
      this.selectedComponents.forEach(componentName => {
        if (!this.componentPorts[componentName]) {
          this.componentPorts[componentName] = this.getDefaultPort(componentName)
        }
      })
    },
    
    getComponentDisplayName(componentName) {
      for (const group of this.formattedComponentGroups) {
        const component = group.components.find(c => c.name === componentName)
        if (component) {
          return component.displayName || component.display_name || componentName
        }
      }
      return componentName
    },

    // 获取表单类型对应的键名
    getFormTypeKey(documentType) {
      const mapping = {
        '安全测评': 'testing',
        '安全监测': 'security',
        '应用加固': 'hardening'
      }
      return mapping[documentType] || 'testing'
    },

    // 获取分组显示名称
    getGroupDisplayName(groupKey) {
      const mapping = {
        'frontend': '前端组件',
        'backend': '后端组件',
        'database': '数据库组件',
        'middleware': '中间件组件',
        'security': '安全组件',
        'monitoring': '监控组件',
        'other': '其他组件'
      }
      return mapping[groupKey] || groupKey
    },
    
    getDefaultPort(componentName) {
      return getComponentDefaultPort(componentName, this.documentType) || '8080'
    },
    

    
    calculateModalPosition() {
      // 自适应尺寸：根据视窗大小动态调整
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight

      // 动态计算宽度
      let modalWidth
      if (viewportWidth >= 1200) {
        modalWidth = Math.min(900, viewportWidth * 0.8) // 大屏幕：最大900px或80%视窗宽度
      } else if (viewportWidth >= 768) {
        modalWidth = Math.min(700, viewportWidth * 0.85) // 中等屏幕：最大700px或85%视窗宽度
      } else {
        modalWidth = viewportWidth - 20 // 小屏幕：几乎全宽，留20px边距
      }

      // 动态计算最大高度
      const maxHeight = viewportHeight - 40 // 留40px上下边距

      this.modalStyle = {
        position: 'fixed',
        top: '40%', // 从50%改为40%，让模态框往上移动
        left: '50%',
        transform: 'translate(-50%, -50%)',
        zIndex: 1055,
        width: `${modalWidth}px`,
        maxWidth: '95vw', // 确保不超出视窗
        maxHeight: `${maxHeight}px`,
        overflow: 'auto'
      }


    },

    // 全选/取消全选
    toggleSelectAll() {
      if (this.selectAllChecked) {
        // 当前是全选状态，执行取消全选
        this.selectedComponents = []
        this.componentPorts = {}
      } else {
        // 当前不是全选状态，执行全选
        this.selectedComponents = this.allComponents.map(component => component.name)

        // 为所有选中的组件初始化端口配置
        this.allComponents.forEach(component => {
          if (!this.componentPorts[component.name]) {
            this.$set(this.componentPorts, component.name, component.port || '8080')
          }
        })
      }
    },

    // 组件选择变化处理
    onComponentSelectionChange(component) {
      if (this.selectedComponents.includes(component.name)) {
        // 选中组件时，初始化端口配置
        if (!this.componentPorts[component.name]) {
          this.$set(this.componentPorts, component.name, component.port || '8080')
        }
      } else {
        // 取消选中时，清除端口配置
        this.$delete(this.componentPorts, component.name)
      }
    },

    // 端口变化处理
    onPortChange(componentName, port) {
      this.$set(this.componentPorts, componentName, port)
    },

    // 更新全选复选框的状态（包括半选状态）
    updateSelectAllState() {
      this.$nextTick(() => {
        const selectAllCheckbox = document.getElementById('selectAll')
        if (selectAllCheckbox) {
          selectAllCheckbox.indeterminate = this.isIndeterminate
        }
      })
    },

    // 获取分类徽章样式
    getCategoryBadgeClass(category) {
      const classMap = {
        'app': 'bg-primary',
        'ops': 'bg-success',
        'server': 'bg-warning text-dark',
        'aimrsk-engine': 'bg-primary',
        'aimrsk-web': 'bg-info',
        'aimrsk-dependence': 'bg-secondary',
        'toolplatform': 'bg-dark',
        'reinforce-engine': 'bg-danger',
        'reinforce-web': 'bg-info',
        'toolplatform-reinforce': 'bg-dark'
      }
      return classMap[category] || 'bg-secondary'
    },

    // 获取分类显示名称
    getCategoryDisplayName(category) {
      const nameMap = {
        'app': '应用',
        'ops': '运维',
        'server': '服务',
        'aimrsk-engine': '引擎',
        'aimrsk-web': 'Web',
        'aimrsk-dependence': '依赖',
        'toolplatform': '平台',
        'reinforce-engine': '加固引擎',
        'reinforce-web': '加固Web',
        'toolplatform-reinforce': '加固平台'
      }
      return nameMap[category] || category
    },

    saveConfiguration() {
      // 清理未选择组件的端口配置
      const cleanedPorts = {}
      this.selectedComponents.forEach(componentName => {
        if (this.componentPorts[componentName]) {
          cleanedPorts[componentName] = this.componentPorts[componentName]
        }
      })

      this.$emit('update', {
        components: this.selectedComponents,
        ports: cleanedPorts
      })

      this.$emit('close')
    }
  },
  watch: {
    selectedComponents: {
      handler(newComponents, oldComponents) {
        // 为新选择的组件设置默认端口
        newComponents.forEach(componentName => {
          if (!this.componentPorts[componentName]) {
            this.componentPorts[componentName] = this.getDefaultPort(componentName)
          }
        })
        
        // 移除未选择组件的端口配置
        if (oldComponents) {
          oldComponents.forEach(componentName => {
            if (!newComponents.includes(componentName)) {
              delete this.componentPorts[componentName]
            }
          })
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-modal-backdrop);
  animation: backdropFadeIn 0.3s ease-out;
}

.modal-container {
  position: fixed;
  z-index: var(--z-modal);
  pointer-events: auto;
}

.modal-dialog {
  margin: 0;
  width: 100%;
  min-width: 320px;
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
}

.modal-content {
  border-radius: var(--radius-lg);
  background: white;
  animation: modalSlideIn 0.3s ease-out;
  border: none;
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 模态框头部样式 */
.modal-header {
  background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
  color: var(--gray-700);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  position: relative;
}

/* 退出按钮样式 */
.btn-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: var(--gray-600);
  text-shadow: none;
  opacity: 1;
  padding: 0.25rem;
  margin: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-close:hover {
  color: var(--gray-800);
  background-color: var(--gray-100);
  transform: scale(1.1);
}

.btn-close:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn-close span {
  font-size: 24px;
  line-height: 1;
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  color: var(--primary-color);
}

.modal-title i {
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}

.btn-close {
  background: var(--gray-100);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all var(--transition-base);
}

.btn-close:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
  color: var(--gray-700);
}

/* 模态框内容区域 */
.modal-body {
  padding: var(--spacing-lg);
  max-height: 70vh;
  overflow-y: auto;
}

.component-group {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  background: white;
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.component-group:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.group-title {
  color: var(--gray-700);
  font-weight: 600;
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
}

.component-list {
  max-height: 240px;
  overflow-y: auto;
}

.form-check {
  margin-bottom: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  background: var(--gray-50);
  border: 1px solid var(--gray-200);
  transition: all var(--transition-base);
}

.form-check:hover {
  background: rgba(0, 123, 255, 0.05);
  border-color: var(--primary-color);
}

.form-check:last-child {
  margin-bottom: 0;
}

.form-check-input {
  margin-top: 0.2rem;
  margin-right: var(--spacing-sm);
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-label {
  font-weight: 500;
  color: var(--gray-700);
  cursor: pointer;
  line-height: 1.5;
}

.port-config-item {
  padding: var(--spacing-lg);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  background: white;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.port-config-item:hover {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.port-config-item:last-child {
  margin-bottom: 0;
}

.port-config-list {
  max-height: 320px;
  overflow-y: auto;
}

/* 表单输入框样式 */
.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  padding: 0.5rem 0.75rem;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  background: white;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
  outline: none;
}

/* 模态框底部 */
.modal-footer {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: var(--font-size-sm);
  transition: all var(--transition-base);
  border: 1px solid transparent;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
  color: var(--gray-800);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-dark);
  border-color: var(--primary-dark);
}

/* 表格样式 */
.table-responsive {
  max-height: 400px;
  overflow-y: auto;
}

.table th {
  position: sticky;
  top: 0;
  background: #f8f9fa;
  z-index: 10;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
}

.table tbody tr:hover {
  background-color: #f5f5f5;
}

.table tbody tr.table-success:hover {
  background-color: #d1e7dd;
}

.form-control-sm {
  font-size: 0.875rem;
}

.badge {
  font-size: 0.75rem;
}

.form-check-label {
  cursor: pointer;
  margin-bottom: 0;
}

/* 全选复选框样式 */
#selectAll {
  cursor: pointer;
}

#selectAll:indeterminate {
  background-color: #0d6efd;
  border-color: #0d6efd;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .modal-dialog {
    min-width: 280px;
  }
}

@media (max-width: 768px) {
  .modal-header {
    padding: 20px 24px;
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .modal-body {
    padding: 24px;
  }

  .modal-footer {
    padding: 20px 24px;
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .component-group {
    padding: 20px;
  }

  .port-config-item {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .modal-dialog {
    width: calc(100vw - 10px);
    max-width: calc(100vw - 10px);
    margin: 5px;
    border-radius: 12px;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-footer {
    padding: 16px 20px;
  }
}
</style>
