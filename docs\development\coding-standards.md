# 💻 编码规范

## 📋 概述

本文档定义了梆梆安全-运维信息登记平台的编码规范，旨在确保代码质量、可读性和可维护性。

## 🐍 Python编码规范

### 代码风格
遵循 [PEP 8](https://www.python.org/dev/peps/pep-0008/) 标准

#### 缩进和空格
```python
# 使用4个空格缩进
def function_name():
    if condition:
        return value
```

#### 行长度
- 每行最多79个字符
- 长表达式可以使用括号换行

```python
# 好的示例
result = some_function(
    argument1,
    argument2,
    argument3
)

# 避免
result = some_function(argument1, argument2, argument3, argument4, argument5)
```

#### 命名规范
```python
# 变量和函数：小写+下划线
user_name = "admin"
def get_user_info():
    pass

# 类名：大驼峰
class UserManager:
    pass

# 常量：大写+下划线
MAX_RETRY_COUNT = 3

# 私有变量：前缀下划线
_private_var = "private"
```

### 导入规范
```python
# 标准库导入
import os
import sys
from datetime import datetime

# 第三方库导入
import flask
from sqlalchemy import Column, Integer

# 本地应用导入
from app.models import User
from app.utils import helper_function
```

### 文档字符串
```python
def calculate_total(items, tax_rate=0.1):
    """
    计算商品总价（含税）
    
    Args:
        items (list): 商品列表
        tax_rate (float): 税率，默认0.1
        
    Returns:
        float: 总价（含税）
        
    Raises:
        ValueError: 当税率为负数时抛出
    """
    if tax_rate < 0:
        raise ValueError("税率不能为负数")
    
    subtotal = sum(item.price for item in items)
    return subtotal * (1 + tax_rate)
```

### 异常处理
```python
# 具体的异常类型
try:
    result = risky_operation()
except ValueError as e:
    logger.error(f"数值错误: {e}")
    return None
except Exception as e:
    logger.error(f"未知错误: {e}")
    raise

# 使用finally清理资源
try:
    file = open('data.txt')
    process_file(file)
finally:
    file.close()
```

## 🌐 JavaScript编码规范

### 代码风格
遵循 [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)

#### 变量声明
```javascript
// 使用const和let，避免var
const API_URL = 'https://api.example.com';
let userCount = 0;

// 对象和数组
const user = {
  name: 'John',
  age: 30,
};

const items = [
  'item1',
  'item2',
  'item3',
];
```

#### 函数定义
```javascript
// 箭头函数（推荐）
const getUserInfo = (userId) => {
  return api.get(`/users/${userId}`);
};

// 传统函数
function calculateTotal(items) {
  return items.reduce((sum, item) => sum + item.price, 0);
}

// 异步函数
const fetchUserData = async (userId) => {
  try {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('获取用户数据失败:', error);
    throw error;
  }
};
```

#### 命名规范
```javascript
// 变量和函数：小驼峰
const userName = 'admin';
const getUserList = () => {};

// 类名：大驼峰
class UserManager {
  constructor() {}
}

// 常量：大写+下划线
const MAX_RETRY_COUNT = 3;
const API_ENDPOINTS = {
  USERS: '/api/users',
  ORDERS: '/api/orders',
};
```

### Vue.js规范

#### 组件命名
```javascript
// 组件文件名：大驼峰
// UserProfile.vue
// ComponentManager.vue

// 组件注册：短横线
Vue.component('user-profile', UserProfile);
```

#### 组件结构
```vue
<template>
  <div class="user-profile">
    <h1>{{ title }}</h1>
    <user-info :user="currentUser" @update="handleUpdate" />
  </div>
</template>

<script>
import UserInfo from './UserInfo.vue';

export default {
  name: 'UserProfile',
  components: {
    UserInfo,
  },
  props: {
    userId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      title: '用户资料',
      currentUser: null,
    };
  },
  computed: {
    isUserLoaded() {
      return this.currentUser !== null;
    },
  },
  methods: {
    async loadUser() {
      try {
        this.currentUser = await this.fetchUser(this.userId);
      } catch (error) {
        this.$message.error('加载用户信息失败');
      }
    },
    handleUpdate(userData) {
      this.currentUser = { ...this.currentUser, ...userData };
    },
  },
  created() {
    this.loadUser();
  },
};
</script>

<style scoped>
.user-profile {
  padding: 20px;
}
</style>
```

## 🗄️ 数据库规范

### 表命名
```sql
-- 表名：小写+下划线
CREATE TABLE user_profile (
    id INT PRIMARY KEY,
    user_name VARCHAR(50),
    created_at TIMESTAMP
);

-- 索引命名
CREATE INDEX idx_user_name ON user_profile(user_name);
CREATE INDEX idx_created_at ON user_profile(created_at);
```

### 字段命名
```sql
-- 字段名：小写+下划线
-- 布尔字段：is_前缀
-- 时间字段：_at后缀
CREATE TABLE excel_file (
    id INT PRIMARY KEY AUTO_INCREMENT,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 📁 文件组织规范

### 目录结构
```
backend/
├── app/
│   ├── models/          # 数据模型
│   ├── routes/          # 路由处理
│   ├── services/        # 业务逻辑
│   ├── utils/           # 工具函数
│   └── __init__.py
├── config.py            # 配置文件
├── requirements.txt     # 依赖列表
└── run.py              # 应用入口

frontend/
├── src/
│   ├── components/      # Vue组件
│   ├── views/          # 页面视图
│   ├── services/       # API服务
│   ├── utils/          # 工具函数
│   └── store/          # 状态管理
├── public/             # 静态资源
└── package.json        # 依赖配置
```

### 文件命名
- Python文件：小写+下划线 (`user_service.py`)
- JavaScript文件：小驼峰 (`userService.js`)
- Vue组件：大驼峰 (`UserProfile.vue`)
- CSS文件：短横线 (`user-profile.css`)

## 🔍 代码审查规范

### 审查要点
1. **功能正确性**: 代码是否实现了预期功能
2. **代码质量**: 是否遵循编码规范
3. **性能考虑**: 是否存在性能问题
4. **安全性**: 是否存在安全漏洞
5. **可维护性**: 代码是否易于理解和维护

### 审查流程
1. **自我审查**: 提交前自己检查代码
2. **同行审查**: 至少一个同事审查
3. **测试验证**: 确保测试通过
4. **文档更新**: 更新相关文档

## 🧪 测试规范

### 测试类型
```python
# 单元测试
import unittest
from app.utils import calculate_total

class TestCalculateTotal(unittest.TestCase):
    def test_calculate_total_with_tax(self):
        items = [MockItem(100), MockItem(200)]
        result = calculate_total(items, 0.1)
        self.assertEqual(result, 330)
    
    def test_calculate_total_zero_tax(self):
        items = [MockItem(100)]
        result = calculate_total(items, 0)
        self.assertEqual(result, 100)
```

```javascript
// 前端测试
import { shallowMount } from '@vue/test-utils';
import UserProfile from '@/components/UserProfile.vue';

describe('UserProfile.vue', () => {
  it('renders user name when passed', () => {
    const user = { name: 'John Doe' };
    const wrapper = shallowMount(UserProfile, {
      propsData: { user }
    });
    expect(wrapper.text()).toMatch('John Doe');
  });
});
```

### 测试覆盖率
- 单元测试覆盖率 ≥ 80%
- 关键业务逻辑覆盖率 ≥ 95%
- 新增代码必须有对应测试

## 📝 注释规范

### Python注释
```python
class UserService:
    """用户服务类
    
    提供用户相关的业务逻辑处理
    """
    
    def create_user(self, user_data):
        """创建新用户
        
        Args:
            user_data (dict): 用户数据
            
        Returns:
            User: 创建的用户对象
        """
        # 验证用户数据
        if not self._validate_user_data(user_data):
            raise ValueError("用户数据无效")
        
        # 创建用户
        user = User(**user_data)
        db.session.add(user)
        db.session.commit()
        
        return user
```

### JavaScript注释
```javascript
/**
 * 计算订单总价
 * @param {Array} items - 商品列表
 * @param {number} taxRate - 税率
 * @returns {number} 总价
 */
const calculateOrderTotal = (items, taxRate = 0.1) => {
  // 计算小计
  const subtotal = items.reduce((sum, item) => sum + item.price, 0);
  
  // 计算税费
  const tax = subtotal * taxRate;
  
  // 返回总价
  return subtotal + tax;
};
```

## 🔧 工具配置

### 代码格式化
```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

### 代码检查
```json
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/standard'
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
};
```

## 📚 相关文档

- [测试指南](testing-guide.md)
- [贡献指南](contribution.md)
- [API文档](../backend/api-documentation.md)
