/*
 * Floating Labels 全局样式
 * 统一的 floating labels 行为：默认标签在内部，有内容或焦点时浮动到上方
 */

/* 基础 floating label 容器 */
.form-floating {
  position: relative;
}

/* 通用输入框样式 - 默认状态 */
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
  padding: 1rem 0.75rem;
  background-color: transparent;
}

/* 标签样式 - 默认在输入框内部 */
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
  color: #6c757d;
  background-color: transparent;
  z-index: 2;
}

/* 激活状态：当输入框获得焦点或有内容时 */
.form-floating > .form-control:focus,
.form-floating > .form-control.has-value,
.form-floating > .form-select:focus,
.form-floating > .form-select.has-value {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

/* 激活状态的标签：浮动到上方并缩小 */
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control.has-value ~ label,
.form-floating > .form-select:focus ~ label,
.form-floating > .form-select.has-value ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: #007bff;
}

/* 特殊处理：date 输入框 */
.form-floating > .form-control[type="date"]:focus,
.form-floating > .form-control[type="date"].has-value {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}

.form-floating > .form-control[type="date"]:focus ~ label,
.form-floating > .form-control[type="date"].has-value ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
  color: #007bff;
}

/* 特殊处理 textarea */
.form-floating > textarea.form-control {
  height: auto;
  min-height: calc(3.5rem + 2px);
}

/* 增强输入框样式 */
.enhanced-input {
  transition: all 0.3s ease;
  border-radius: 8px;
  border: 2px solid #e9ecef;
}

.enhanced-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

.enhanced-input:readonly {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* 必填字段标识 */
.form-floating > label .text-danger {
  color: #dc3545 !important;
}

/* 帮助文本样式 */
.form-floating + small.text-muted {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #6c757d;
}

/* 图标样式 */
.form-floating > label i {
  margin-right: 0.25rem;
  color: #6c757d;
}

.form-floating > .form-control:focus ~ label i,
.form-floating > .form-control:not(:placeholder-shown) ~ label i,
.form-floating > .form-select:focus ~ label i,
.form-floating > .form-select:not([value=""]) ~ label i {
  color: #007bff;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .form-floating > .form-control,
  .form-floating > .form-select {
    font-size: 1rem;
  }
  
  .form-floating > label {
    font-size: 1rem;
  }
}

/* 错误状态样式 */
.form-floating > .form-control.is-invalid,
.form-floating > .form-select.is-invalid {
  border-color: #dc3545;
}

.form-floating > .form-control.is-invalid:focus,
.form-floating > .form-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-floating > .form-control.is-invalid ~ label,
.form-floating > .form-select.is-invalid ~ label {
  color: #dc3545;
}

/* 成功状态样式 */
.form-floating > .form-control.is-valid,
.form-floating > .form-select.is-valid {
  border-color: #198754;
}

.form-floating > .form-control.is-valid:focus,
.form-floating > .form-select.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.form-floating > .form-control.is-valid ~ label,
.form-floating > .form-select.is-valid ~ label {
  color: #198754;
}
