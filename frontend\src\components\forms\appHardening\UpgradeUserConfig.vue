<template>
  <div class="upgrade-user-config">
    <!-- 升级用户配置 - 使用与其他用户信息一致的样式 -->
    <div class="row g-3">
      <div class="col-md-6">
        <div class="account-card">
          <div class="account-card-header">
            <i class="bi bi-arrow-up-circle me-1 text-success"></i>
            <span class="fw-bold">升级用户</span>
            <button
              type="button"
              class="btn btn-sm btn-outline-secondary ms-auto"
              @click="resetToDefault"
              title="重置为默认值"
            >
              <i class="bi bi-arrow-clockwise"></i>
            </button>
          </div>
          <div class="account-card-body">
            <div class="input-group mb-2">
              <span class="input-group-text">账号</span>
              <input
                type="text"
                class="form-control"
                v-model="localUserConfig.username"
                :class="{ 'is-invalid': errors.username }"
                placeholder="升级用户账号"
                @input="validateUsername"
                @blur="validateUsername"
              >
            </div>
            <div class="input-group">
              <span class="input-group-text">密码</span>
              <input
                :type="showPassword ? 'text' : 'password'"
                class="form-control"
                v-model="localUserConfig.password"
                :class="{ 'is-invalid': errors.password }"
                placeholder="升级用户密码"
                @input="validatePassword"
                @blur="validatePassword"
              >
              <button
                class="btn btn-outline-secondary"
                type="button"
                @click="togglePassword"
              >
                <i :class="showPassword ? 'bi bi-eye-slash' : 'bi bi-eye'"></i>
              </button>
            </div>

            <!-- 错误提示 -->
            <div v-if="errors.username || errors.password" class="mt-2">
              <div v-if="errors.username" class="text-danger small">
                <i class="bi bi-exclamation-circle me-1"></i>
                {{ errors.username }}
              </div>
              <div v-if="errors.password" class="text-danger small">
                <i class="bi bi-exclamation-circle me-1"></i>
                {{ errors.password }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 配置说明 -->
      <div class="col-md-6">
        <div class="config-info-card">
          <div class="config-info-header">
            <i class="bi bi-info-circle me-1 text-info"></i>
            <span class="fw-bold">使用说明</span>
          </div>
          <div class="config-info-body">
            <div class="info-item">
              <i class="bi bi-arrow-up-circle text-success me-2"></i>
              <span class="text-muted">用于访问升级平台进行系统更新操作</span>
            </div>
            <div class="info-item">
              <i class="bi bi-shield-check text-success me-2"></i>
              <span class="text-muted">默认凭据已预设，可根据实际需求修改</span>
            </div>
            <div class="info-item">
              <i class="bi bi-gear text-primary me-2"></i>
              <span class="text-muted">支持一键重置为默认配置</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 升级用户配置组件
 * 复用登录界面的样式和交互模式
 * 用于配置应用加固表单中的升级用户信息
 */
export default {
  name: 'UpgradeUserConfig',
  
  props: {
    // 升级用户配置数据
    userConfig: {
      type: Object,
      default: () => ({
        username: 'upgrader',
        password: 'upgrader@abc#2020'
      })
    },
    // 是否禁用编辑
    disabled: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:userConfig', 'config-change'],

  data() {
    return {
      // 本地用户配置数据
      localUserConfig: {
        username: '',
        password: ''
      },
      // 是否显示密码
      showPassword: false,
      // 表单验证错误
      errors: {},
      // 默认配置
      defaultConfig: {
        username: 'upgrader',
        password: 'upgrader@abc#2020'
      }
    }
  },

  watch: {
    // 监听外部配置变化
    userConfig: {
      handler(newConfig) {
        if (newConfig) {
          this.localUserConfig = { ...newConfig }
        } else {
          this.localUserConfig = { ...this.defaultConfig }
        }
      },
      immediate: true,
      deep: true
    },
    // 监听本地配置变化，向外发送事件
    localUserConfig: {
      handler(newConfig) {
        this.$emit('update:userConfig', { ...newConfig })
        this.$emit('config-change', { ...newConfig })
      },
      deep: true
    }
  },

  mounted() {
    // 初始化配置
    this.initializeConfig()
  },

  methods: {
    /**
     * 初始化配置
     */
    initializeConfig() {
      if (this.userConfig && Object.keys(this.userConfig).length > 0) {
        this.localUserConfig = { ...this.userConfig }
      } else {
        this.localUserConfig = { ...this.defaultConfig }
      }
    },

    /**
     * 切换密码显示状态
     */
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    /**
     * 重置为默认配置
     */
    resetToDefault() {
      this.localUserConfig = { ...this.defaultConfig }
      this.errors = {}
      this.showPassword = false
    },

    /**
     * 验证用户名
     */
    validateUsername() {
      const username = this.localUserConfig.username
      
      if (!username) {
        this.errors.username = '升级用户名不能为空'
        return false
      }
      
      if (username.length < 3) {
        this.errors.username = '用户名长度不能少于3个字符'
        return false
      }
      
      if (username.length > 50) {
        this.errors.username = '用户名长度不能超过50个字符'
        return false
      }
      
      // 清除错误
      delete this.errors.username
      return true
    },

    /**
     * 验证密码
     */
    validatePassword() {
      const password = this.localUserConfig.password
      
      if (!password) {
        this.errors.password = '升级密码不能为空'
        return false
      }
      
      if (password.length < 6) {
        this.errors.password = '密码长度不能少于6个字符'
        return false
      }
      
      if (password.length > 100) {
        this.errors.password = '密码长度不能超过100个字符'
        return false
      }
      
      // 清除错误
      delete this.errors.password
      return true
    },

    /**
     * 验证整个表单
     */
    validateForm() {
      const usernameValid = this.validateUsername()
      const passwordValid = this.validatePassword()
      
      return usernameValid && passwordValid
    },

    /**
     * 获取配置数据
     */
    getConfig() {
      return { ...this.localUserConfig }
    },

    /**
     * 检查配置是否有效
     */
    isValid() {
      return this.validateForm()
    }
  }
}
</script>

<style scoped>
/* 与现有用户信息样式保持一致 */
.upgrade-user-config {
  width: 100%;
}

/* 账号卡片样式 - 与AccessInfoSection保持一致 */
.account-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.account-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.account-card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.account-card-body {
  padding: 16px;
}

/* 输入组样式 - 与现有样式保持一致 */
.input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
  color: #495057;
  font-weight: 500;
  min-width: 60px;
  justify-content: center;
}

.form-control {
  border-color: #ced4da;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* 密码切换按钮样式 */
.btn-outline-secondary {
  border-color: #6c757d;
  color: #6c757d;
  transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
  background-color: #6c757d;
  border-color: #6c757d;
  color: #ffffff;
}

.btn-outline-secondary:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
}

/* 配置说明卡片 */
.config-info-card {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  height: 100%;
}

.config-info-header {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
}

.config-info-body {
  padding: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
  font-size: 0.85rem;
  line-height: 1.4;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item i {
  margin-top: 2px;
  flex-shrink: 0;
}

/* 错误提示样式 */
.text-danger.small {
  font-size: 0.8rem;
  margin-bottom: 4px;
}

/* 重置按钮样式 */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .account-card-header,
  .config-info-header {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .account-card-body,
  .config-info-body {
    padding: 12px;
  }

  .info-item {
    font-size: 0.8rem;
  }

  .btn-sm {
    padding: 0.2rem 0.4rem;
    font-size: 0.7rem;
  }
}

/* 确保与其他用户信息卡片的间距一致 */
.row.g-3 > .col-md-6 {
  margin-bottom: 1rem;
}

@media (min-width: 768px) {
  .row.g-3 > .col-md-6 {
    margin-bottom: 0;
  }
}
</style>
