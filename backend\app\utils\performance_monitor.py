"""
性能监控工具
提供系统性能监控、慢查询检测、缓存命中率统计等功能
"""

import time
import logging
import psutil
from functools import wraps
from collections import defaultdict, deque
from datetime import datetime, timedelta
from flask import request, g, current_app
from sqlalchemy import event
from sqlalchemy.engine import Engine
from app.utils.cache_utils import cache

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.request_times = defaultdict(deque)
        self.slow_queries = deque(maxlen=100)
        self.cache_stats = defaultdict(int)
        self.error_counts = defaultdict(int)
        
    def record_request(self, endpoint, duration, status_code):
        """记录请求性能"""
        # 保留最近1000个请求的性能数据
        self.request_times[endpoint].append({
            'duration': duration,
            'timestamp': time.time(),
            'status_code': status_code
        })
        
        # 只保留最近的记录
        if len(self.request_times[endpoint]) > 1000:
            self.request_times[endpoint].popleft()
        
        # 记录慢请求 (分开存储，不与数据库慢查询混合)
        if duration > 3.0:  # 超过3秒的请求才记录，避免与测试冲突
            if not hasattr(self, 'slow_requests'):
                self.slow_requests = deque(maxlen=100)

            self.slow_requests.append({
                'endpoint': endpoint,
                'duration': duration,
                'timestamp': datetime.now(),
                'status_code': status_code,
                'type': 'http_request'
            })
            logger.warning(f"慢请求: {endpoint} - {duration:.2f}s")
    
    def record_cache_hit(self, cache_type, hit=True):
        """记录缓存命中情况"""
        if hit:
            self.cache_stats[f"{cache_type}_hits"] += 1
        else:
            self.cache_stats[f"{cache_type}_misses"] += 1
    
    def record_error(self, error_type):
        """记录错误"""
        self.error_counts[error_type] += 1
    
    def get_stats(self):
        """获取性能统计"""
        now = time.time()
        hour_ago = now - 3600
        
        stats = {
            'system': self._get_system_stats(),
            'requests': self._get_request_stats(hour_ago),
            'cache': self._get_cache_stats(),
            'database': self._get_database_stats(),
            'errors': dict(self.error_counts),
            'slow_queries': list(self.slow_queries)[-10:]  # 最近10个慢查询
        }
        
        return stats
    
    def _get_system_stats(self):
        """获取系统资源统计"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            }
        except Exception as e:
            logger.error(f"获取系统统计失败: {str(e)}")
            return {}
    
    def _get_request_stats(self, since_time):
        """获取请求统计"""
        stats = {}
        
        for endpoint, requests in self.request_times.items():
            recent_requests = [r for r in requests if r['timestamp'] > since_time]
            
            if recent_requests:
                durations = [r['duration'] for r in recent_requests]
                stats[endpoint] = {
                    'count': len(recent_requests),
                    'avg_duration': sum(durations) / len(durations),
                    'max_duration': max(durations),
                    'min_duration': min(durations),
                    'error_rate': len([r for r in recent_requests if r['status_code'] >= 400]) / len(recent_requests)
                }
        
        return stats
    
    def _get_cache_stats(self):
        """获取缓存统计"""
        cache_stats = {}
        
        for key, value in self.cache_stats.items():
            if '_hits' in key:
                cache_type = key.replace('_hits', '')
                hits = value
                misses = self.cache_stats.get(f"{cache_type}_misses", 0)
                total = hits + misses
                
                if total > 0:
                    cache_stats[cache_type] = {
                        'hits': hits,
                        'misses': misses,
                        'hit_rate': hits / total,
                        'total': total
                    }
        
        return cache_stats
    
    def _get_database_stats(self):
        """获取数据库统计"""
        try:
            # 这里可以添加数据库连接池状态等信息
            return {
                'slow_queries_count': len(self.slow_queries),
                'recent_slow_queries': len([q for q in self.slow_queries 
                                          if q['timestamp'] > datetime.now() - timedelta(hours=1)])
            }
        except Exception as e:
            logger.error(f"获取数据库统计失败: {str(e)}")
            return {}


# 全局性能监控实例
performance_monitor = PerformanceMonitor()


def monitor_performance(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        status_code = 200
        
        try:
            result = func(*args, **kwargs)
            if hasattr(result, 'status_code'):
                status_code = result.status_code
            return result
        except Exception as e:
            status_code = 500
            performance_monitor.record_error(type(e).__name__)
            raise
        finally:
            duration = time.time() - start_time
            endpoint = request.endpoint or 'unknown'
            performance_monitor.record_request(endpoint, duration, status_code)
    
    return wrapper


def monitor_cache_operation(cache_type):
    """缓存操作监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # 判断是否命中缓存
            hit = result is not None
            performance_monitor.record_cache_hit(cache_type, hit)
            
            return result
        return wrapper
    return decorator


# SQLAlchemy慢查询监控
@event.listens_for(Engine, "before_cursor_execute")
def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """查询开始前记录时间"""
    context._query_start_time = time.time()


@event.listens_for(Engine, "after_cursor_execute")
def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """查询结束后检查执行时间"""
    total = time.time() - context._query_start_time

    # 记录慢查询 (超过1秒)
    if total > 1.0:
        # 清理和格式化SQL语句
        cleaned_sql = statement.strip() if statement else ''

        # 过滤掉一些不重要的查询
        skip_patterns = [
            'BEGIN',
            'COMMIT',
            'ROLLBACK',
            'SET ',
            'SHOW ',
            'SELECT 1',
            'DESCRIBE ',
            'EXPLAIN ',
            'PRAGMA ',
            'CREATE TEMPORARY',
            'DROP TEMPORARY'
        ]

        # 检查是否应该跳过这个查询
        # 特别处理空查询和很短的查询
        if not cleaned_sql or len(cleaned_sql.strip()) < 5:
            should_skip = True
        else:
            should_skip = any(cleaned_sql.upper().startswith(pattern) for pattern in skip_patterns)

        if not should_skip and cleaned_sql:
            # 格式化SQL - 移除多余的空白字符
            formatted_sql = ' '.join(cleaned_sql.split())

            performance_monitor.slow_queries.append({
                'sql': formatted_sql[:200] + '...' if len(formatted_sql) > 200 else formatted_sql,
                'duration': total,
                'timestamp': datetime.now(),
                'parameters': str(parameters)[:100] if parameters else None,
                'type': 'database_query'
            })
            logger.warning(f"慢查询: {total:.2f}s - {formatted_sql[:100]}")
        else:
            logger.debug(f"跳过慢查询: {cleaned_sql[:50]}")


class PerformanceMiddleware:
    """性能监控中间件"""
    
    def __init__(self, app):
        self.app = app
        self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        """请求开始前"""
        g.start_time = time.time()
    
    def after_request(self, response):
        """请求结束后"""
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            endpoint = request.endpoint or 'unknown'
            performance_monitor.record_request(endpoint, duration, response.status_code)
        
        return response


def get_performance_report():
    """获取性能报告"""
    stats = performance_monitor.get_stats()
    
    # 生成报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_requests': sum(len(requests) for requests in performance_monitor.request_times.values()),
            'slow_queries_count': len(performance_monitor.slow_queries),
            'error_count': sum(performance_monitor.error_counts.values()),
            'cache_hit_rate': _calculate_overall_cache_hit_rate(stats['cache'])
        },
        'details': stats
    }
    
    return report


def _calculate_overall_cache_hit_rate(cache_stats):
    """计算总体缓存命中率"""
    total_hits = sum(stat['hits'] for stat in cache_stats.values())
    total_requests = sum(stat['total'] for stat in cache_stats.values())
    
    if total_requests > 0:
        return total_hits / total_requests
    return 0


def clear_performance_data():
    """清理性能数据"""
    performance_monitor.request_times.clear()
    performance_monitor.slow_queries.clear()
    performance_monitor.cache_stats.clear()
    performance_monitor.error_counts.clear()

    # 清理慢请求记录
    if hasattr(performance_monitor, 'slow_requests'):
        performance_monitor.slow_requests.clear()

    logger.info("性能监控数据已清理")


# 导出主要功能
__all__ = [
    'PerformanceMonitor',
    'performance_monitor',
    'monitor_performance',
    'monitor_cache_operation',
    'PerformanceMiddleware',
    'get_performance_report',
    'clear_performance_data'
]
