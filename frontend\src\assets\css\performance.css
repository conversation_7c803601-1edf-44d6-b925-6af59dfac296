/* ==================== 高性能CSS优化系统 ==================== */

/* 全局性能基础 */
* {
  box-sizing: border-box;
}

/* 启用硬件加速的元素 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* ==================== 优化的过渡动画 ==================== */
.btn,
.card,
.form-control,
.form-select {
  transition: transform 0.15s ease, box-shadow 0.15s ease, background-color 0.15s ease;
  will-change: auto;
}

/* 表格行优化 - 移除复杂过渡 */
.table tr {
  transition: background-color 0.1s ease;
}

/* ==================== 简化的阴影系统 ==================== */
.card {
  box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  border: 1px solid rgba(0,0,0,0.06);
}

.card:hover {
  box-shadow: 0 2px 6px rgba(0,0,0,0.12);
}

.card-enhanced {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* ==================== 高性能表格 ==================== */
.table {
  table-layout: fixed;
  border-collapse: collapse;
}

.table th,
.table td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0.75rem 0.5rem;
}

/* 虚拟滚动优化 */
.table-responsive {
  -webkit-overflow-scrolling: touch;
  contain: layout;
  overflow-x: auto;
  overflow-y: hidden;
}

/* ==================== 简化的按钮样式 ==================== */
.btn-primary {
  background-color: #1e40af;
  border-color: #1e40af;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

/* ==================== 字体和图标优化 ==================== */
.bi {
  font-display: swap;
  font-feature-settings: "liga" 1;
}

body {
  font-display: swap;
  text-rendering: optimizeSpeed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==================== 布局容器优化 ==================== */
.container-fluid {
  contain: layout style;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 2px rgba(0,0,0,0.06);
  border: 1px solid rgba(0,0,0,0.04);
}

/* ==================== 徽章和标签优化 ==================== */
.badge {
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.75rem;
  padding: 0.25em 0.5em;
}

/* ==================== 表单控件优化 ==================== */
.form-control,
.form-select {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  transition: border-color 0.15s ease, box-shadow 0.15s ease;
}

.form-control:focus,
.form-select:focus {
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
  outline: none;
}

/* ==================== 响应式性能优化 ==================== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 移动端性能优化 */
@media (max-width: 768px) {
  .card:hover {
    transform: none;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
  }

  .btn:hover {
    transform: none;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .table th,
  .table td {
    padding: 0.5rem 0.25rem;
  }
}

/* ==================== 加载状态优化 ==================== */
.loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid rgba(0,0,0,0.1);
  border-radius: 50%;
  border-top-color: #1e40af;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* ==================== 内容优化 ==================== */
.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
