# Export Excel Backend

这是一个基于 Flask 的后端应用程序，用于生成和管理 Excel 文件。

## 功能特性

- 表单数据管理
- Excel 文件生成
- 模板管理
- 历史记录追踪
- 用户认证和授权

## 技术栈

- Flask - Web 框架
- SQLAlchemy - ORM
- pandas - 数据处理
- openpyxl - Excel 文件操作
- Jinja2 - 模板引擎

## 安装和运行

### 使用 uv (推荐)

uv 是一个现代的 Python 包管理器，比 pip 更快更可靠。

#### 安装 uv

```bash
# Windows (PowerShell)
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"

# Linux/Mac
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 快速启动

```bash
# Windows - 直接运行批处理文件
start.bat

# 手动方式（推荐）
uv sync                        # 自动创建虚拟环境并安装所有依赖
source .venv/Scripts/activate  # 激活虚拟环境 (Windows)
source .venv/bin/activate      # 激活虚拟环境 (Linux/Mac)
python run.py                  # 运行应用

# 或者一步到位
uv sync && source .venv/Scripts/activate && python run.py
```

#### 开发环境

```bash
# 安装开发依赖
uv sync --dev

# 启动开发环境
source .venv/Scripts/activate
export FLASK_ENV=development
export FLASK_DEBUG=1
python run.py

# 代码格式化和检查
uv run black .                # 格式化代码
uv run flake8 .               # 代码检查
uv run mypy .                 # 类型检查
uv run pytest                # 运行测试
```

### 使用传统方式

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 运行应用
python run.py
```

## 配置

应用程序使用 SQLite 数据库，数据库文件位于 `app.db`。

## 开发

- 开发服务器运行在 `http://localhost:5000`
- 支持热重载
- 包含完整的 API 文档

## uv 管理优势

- **更快的依赖解析**: uv 比 pip 快 10-100 倍
- **更好的依赖管理**: 自动解决版本冲突
- **统一的工具链**: 包管理、虚拟环境、项目管理一体化
- **更可靠的构建**: 确定性的依赖解析和安装

## 可用脚本

- `start.bat` - Windows 快速启动脚本
- `Makefile` - 跨平台构建脚本

## 项目结构

```
backend/
├── app/                 # 应用程序主目录
├── excel_files/         # Excel 文件存储
├── static/             # 静态文件
├── migrations/         # 数据库迁移
├── .venv/              # uv 管理的虚拟环境
├── pyproject.toml      # uv 项目配置
├── uv.lock             # uv 锁定文件
├── requirements.txt    # 传统依赖列表 (兼容性)
├── start.bat           # Windows 启动脚本
├── Makefile            # 跨平台构建脚本
└── run.py             # 应用入口
```

## 常用命令

```bash
# 使用 uv 管理依赖
uv sync                 # 同步依赖（推荐，自动创建虚拟环境）
uv add <package>        # 添加依赖
uv remove <package>     # 移除依赖
uv lock                 # 锁定依赖版本

# 虚拟环境管理
uv venv                 # 创建虚拟环境
uv venv --python 3.9    # 指定 Python 版本

# 运行命令
source .venv/Scripts/activate && python run.py  # 启动应用
uv run pytest          # 运行测试
uv run black .          # 格式化代码
uv run flake8 .         # 代码检查
```

## 快速开始

1. **安装 uv**（如果还没有安装）
2. **同步依赖**: `uv sync`
3. **启动应用**: `source .venv/Scripts/activate && python run.py`
4. **访问应用**: http://localhost:5000

就这么简单！uv 会自动处理虚拟环境创建和依赖安装。
