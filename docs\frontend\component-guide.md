# 🎨 前端组件开发指南

## 📋 概述

本指南介绍了梆梆安全-运维信息登记平台前端组件的开发规范和最佳实践。

## 🏗️ 组件架构

### 组件分类
```
src/components/
├── forms/              # 表单组件
│   ├── common/         # 通用表单组件
│   ├── securityTesting/    # 安全测评表单
│   ├── securityMonitoring/ # 安全监测表单
│   └── appHardening/       # 应用加固表单
├── modals/             # 模态框组件
├── layout/             # 布局组件
└── ui/                 # UI基础组件
```

## 📝 组件开发规范

### 组件命名
```javascript
// 组件文件名：大驼峰
UserProfile.vue
ComponentManager.vue
FormNavigation.vue

// 组件注册名：短横线
<user-profile />
<component-manager />
<form-navigation />
```

### 组件结构
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    // 属性定义
  },
  data() {
    return {
      // 数据定义
    };
  },
  computed: {
    // 计算属性
  },
  methods: {
    // 方法定义
  },
  created() {
    // 生命周期钩子
  }
};
</script>

<style scoped>
/* 组件样式 */
</style>
```

## 🎯 表单组件开发

### 通用表单组件

#### FloatingLabelInput
```vue
<template>
  <div class="floating-label-input">
    <input
      :id="inputId"
      v-model="inputValue"
      :type="type"
      :required="required"
      :placeholder="placeholder"
      @focus="onFocus"
      @blur="onBlur"
    />
    <label :for="inputId" :class="{ active: isActive }">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>
  </div>
</template>

<script>
export default {
  name: 'FloatingLabelInput',
  props: {
    label: String,
    type: { type: String, default: 'text' },
    required: Boolean,
    placeholder: String,
    modelValue: [String, Number]
  },
  emits: ['update:modelValue'],
  computed: {
    inputValue: {
      get() { return this.modelValue; },
      set(value) { this.$emit('update:modelValue', value); }
    },
    isActive() {
      return this.isFocused || this.inputValue;
    }
  }
};
</script>
```

#### CollapsibleSection
```vue
<template>
  <div class="collapsible-section">
    <div class="section-header" @click="toggle">
      <i :class="iconClass"></i>
      <h3>{{ title }}</h3>
      <i class="bi-chevron-down" :class="{ rotated: isExpanded }"></i>
    </div>
    <transition name="slide">
      <div v-show="isExpanded" class="section-content">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'CollapsibleSection',
  props: {
    title: String,
    icon: String,
    expanded: { type: Boolean, default: false }
  },
  data() {
    return {
      isExpanded: this.expanded
    };
  },
  computed: {
    iconClass() {
      return `bi-${this.icon}`;
    }
  },
  methods: {
    toggle() {
      this.isExpanded = !this.isExpanded;
    }
  }
};
</script>
```

### 表单导航组件

#### FormNavigation
```vue
<template>
  <div class="form-navigation" :class="{ 'is-mobile': isMobile }">
    <div class="nav-header">
      <h4>表单导航</h4>
      <button @click="togglePosition" class="position-toggle">
        <i class="bi-arrow-left-right"></i>
      </button>
    </div>
    
    <nav class="nav-sections">
      <a
        v-for="section in sections"
        :key="section.id"
        :href="`#${section.id}`"
        :class="{ active: activeSection === section.id }"
        @click="scrollToSection(section.id)"
      >
        <i :class="section.icon"></i>
        {{ section.title }}
      </a>
    </nav>
    
    <div class="nav-actions">
      <button @click="saveForm" class="btn btn-primary">
        <i class="bi-save"></i>
        保存表单
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormNavigation',
  props: {
    sections: Array
  },
  data() {
    return {
      activeSection: '',
      isMobile: false
    };
  },
  methods: {
    scrollToSection(sectionId) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    },
    togglePosition() {
      this.$emit('toggle-position');
    },
    saveForm() {
      this.$emit('save-form');
    }
  }
};
</script>
```

## 🎨 UI组件开发

### 按钮组件
```vue
<template>
  <button
    :class="buttonClass"
    :disabled="disabled"
    @click="handleClick"
  >
    <i v-if="icon" :class="iconClass"></i>
    <slot></slot>
  </button>
</template>

<script>
export default {
  name: 'BaseButton',
  props: {
    variant: { type: String, default: 'primary' },
    size: { type: String, default: 'md' },
    icon: String,
    disabled: Boolean,
    loading: Boolean
  },
  computed: {
    buttonClass() {
      return [
        'btn',
        `btn-${this.variant}`,
        `btn-${this.size}`,
        { 'btn-loading': this.loading }
      ];
    },
    iconClass() {
      return `bi-${this.icon}`;
    }
  },
  methods: {
    handleClick(event) {
      if (!this.disabled && !this.loading) {
        this.$emit('click', event);
      }
    }
  }
};
</script>
```

### 模态框组件
```vue
<template>
  <teleport to="body">
    <transition name="modal">
      <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
        <div class="modal-container" @click.stop>
          <div class="modal-header">
            <h3>{{ title }}</h3>
            <button @click="close" class="modal-close">
              <i class="bi-x"></i>
            </button>
          </div>
          
          <div class="modal-body">
            <slot></slot>
          </div>
          
          <div class="modal-footer" v-if="$slots.footer">
            <slot name="footer"></slot>
          </div>
        </div>
      </div>
    </transition>
  </teleport>
</template>

<script>
export default {
  name: 'BaseModal',
  props: {
    visible: Boolean,
    title: String,
    closeOnOverlay: { type: Boolean, default: true }
  },
  emits: ['close'],
  methods: {
    close() {
      this.$emit('close');
    },
    handleOverlayClick() {
      if (this.closeOnOverlay) {
        this.close();
      }
    }
  }
};
</script>
```

## 🔧 组件通信

### Props传递
```javascript
// 父组件
<child-component
  :user-data="userData"
  :is-loading="loading"
  @user-updated="handleUserUpdate"
/>

// 子组件
export default {
  props: {
    userData: Object,
    isLoading: Boolean
  },
  emits: ['user-updated'],
  methods: {
    updateUser(data) {
      this.$emit('user-updated', data);
    }
  }
};
```

### 事件总线
```javascript
// eventBus.js
import { createApp } from 'vue';
export const eventBus = createApp({}).config.globalProperties;

// 发送事件
eventBus.$emit('form-saved', formData);

// 监听事件
eventBus.$on('form-saved', (data) => {
  console.log('表单已保存:', data);
});
```

### Vuex状态管理
```javascript
// store/modules/form.js
export default {
  namespaced: true,
  state: {
    formData: {},
    isLoading: false
  },
  mutations: {
    SET_FORM_DATA(state, data) {
      state.formData = data;
    },
    SET_LOADING(state, loading) {
      state.isLoading = loading;
    }
  },
  actions: {
    async saveForm({ commit }, formData) {
      commit('SET_LOADING', true);
      try {
        await api.saveForm(formData);
        commit('SET_FORM_DATA', formData);
      } finally {
        commit('SET_LOADING', false);
      }
    }
  }
};
```

## 🎯 最佳实践

### 组件设计原则
1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 设计通用的、可复用的组件
3. **可维护性**: 代码结构清晰，易于理解和修改
4. **性能优化**: 合理使用计算属性和监听器

### 性能优化
```javascript
// 使用计算属性缓存复杂计算
computed: {
  filteredItems() {
    return this.items.filter(item => item.active);
  }
},

// 使用v-memo优化列表渲染
<template v-for="item in items" :key="item.id" v-memo="[item.id, item.name]">
  <item-component :item="item" />
</template>

// 懒加载组件
const LazyComponent = defineAsyncComponent(() => import('./LazyComponent.vue'));
```

### 错误处理
```javascript
// 组件错误边界
export default {
  errorCaptured(err, instance, info) {
    console.error('组件错误:', err);
    console.error('错误信息:', info);
    return false; // 阻止错误继续传播
  }
};

// API错误处理
async methods: {
  async loadData() {
    try {
      this.loading = true;
      const data = await api.getData();
      this.data = data;
    } catch (error) {
      this.error = error.message;
      this.$message.error('数据加载失败');
    } finally {
      this.loading = false;
    }
  }
}
```

## 🧪 组件测试

### 单元测试
```javascript
import { mount } from '@vue/test-utils';
import UserProfile from '@/components/UserProfile.vue';

describe('UserProfile.vue', () => {
  it('renders user name when passed', () => {
    const user = { name: 'John Doe' };
    const wrapper = mount(UserProfile, {
      props: { user }
    });
    expect(wrapper.text()).toMatch('John Doe');
  });

  it('emits update event when user is modified', async () => {
    const wrapper = mount(UserProfile);
    await wrapper.find('button').trigger('click');
    expect(wrapper.emitted()).toHaveProperty('user-updated');
  });
});
```

### 集成测试
```javascript
import { mount } from '@vue/test-utils';
import { createStore } from 'vuex';
import FormManager from '@/components/FormManager.vue';

describe('FormManager.vue', () => {
  let store;

  beforeEach(() => {
    store = createStore({
      modules: {
        form: formModule
      }
    });
  });

  it('saves form data to store', async () => {
    const wrapper = mount(FormManager, {
      global: {
        plugins: [store]
      }
    });

    await wrapper.find('form').trigger('submit');
    expect(store.state.form.formData).toBeDefined();
  });
});
```

## 📚 相关文档

- [开发环境配置](development.md)
- [构建部署指南](build-deployment.md)
- [编码规范](../development/coding-standards.md)
- [API接口文档](../backend/api-documentation.md)
