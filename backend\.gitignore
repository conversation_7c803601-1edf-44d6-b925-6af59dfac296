# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# uv
.venv/
uv.lock

# Flask
instance/
.webassets-cache

# Database
*.db
*.sqlite
*.sqlite3
app.db
app_backup_*.db

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Excel files (generated)
excel_files/generated/*.xlsx
excel_files/generated/*.xls

# Backup files
db_backup/*dev*.db
*.backup

# Cache
.cache/
.pytest_cache/
