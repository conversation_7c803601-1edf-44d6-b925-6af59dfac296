# 🔧 故障排除指南

## 📋 概述

本指南提供了梆梆安全-运维信息登记平台常见问题的解决方案和故障排除方法。

## 🚨 常见问题分类

### 🔌 连接问题

#### 数据库连接失败
**症状**: 应用启动时报数据库连接错误
**可能原因**:
- MySQL服务未启动
- 数据库配置错误
- 网络连接问题
- 用户权限不足

**解决方案**:
```bash
# 1. 检查MySQL服务状态
sudo systemctl status mysql
sudo systemctl start mysql

# 2. 检查网络连接
telnet ************ 3306
ping ************

# 3. 测试数据库连接
mysql -h ************ -u junguangchen -p

# 4. 检查用户权限
SHOW GRANTS FOR 'junguangchen'@'%';
```

#### Redis连接失败
**症状**: 缓存功能不工作，日志显示Redis连接错误
**解决方案**:
```bash
# 1. 检查Redis服务
sudo systemctl status redis
sudo systemctl start redis

# 2. 测试Redis连接
redis-cli -h ************ -p 6379 -a 1qaz@WSX ping

# 3. 检查Redis配置
redis-cli -h ************ -p 6379 -a 1qaz@WSX info
```

#### 前端API请求失败
**症状**: 前端页面无法加载数据，控制台显示API请求错误
**解决方案**:
```bash
# 1. 检查后端服务状态
ps aux | grep python
netstat -tlnp | grep 5000

# 2. 检查nginx配置
sudo nginx -t
sudo systemctl status nginx

# 3. 测试API端点
curl http://localhost:5000/health
curl http://localhost:9999/api/health
```

### 🐍 Python环境问题

#### 依赖包缺失
**症状**: ImportError或ModuleNotFoundError
**解决方案**:
```bash
# 1. 激活虚拟环境
source venv/bin/activate  # 或 source .venv/bin/activate

# 2. 重新安装依赖
pip install -r requirements.txt

# 3. 检查已安装包
pip list
pip show <package_name>

# 4. 使用uv管理依赖（推荐）
uv sync
```

#### Python版本不兼容
**症状**: 语法错误或功能不支持
**解决方案**:
```bash
# 1. 检查Python版本
python --version
python3 --version

# 2. 使用正确的Python版本
python3.8 -m venv venv
# 或
uv venv --python 3.8
```

#### 虚拟环境问题
**症状**: 包安装后仍然找不到
**解决方案**:
```bash
# 1. 确认虚拟环境激活
which python
echo $VIRTUAL_ENV

# 2. 重新创建虚拟环境
rm -rf venv
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 🌐 前端问题

#### 构建失败
**症状**: npm run build 失败
**解决方案**:
```bash
# 1. 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json

# 2. 重新安装依赖
npm install

# 3. 检查Node.js版本
node --version
npm --version

# 4. 使用yarn替代npm
yarn install
yarn build
```

#### 页面空白或加载失败
**症状**: 浏览器显示空白页面
**解决方案**:
```bash
# 1. 检查控制台错误
# 打开浏览器开发者工具查看错误信息

# 2. 检查API配置
# 确认.env文件中的API地址正确

# 3. 检查路由配置
# 确认Vue Router配置正确

# 4. 检查nginx配置
# 确认try_files配置支持SPA路由
```

### 📊 Excel相关问题

#### Excel文件生成失败
**症状**: 提交表单后无法生成Excel文件
**解决方案**:
```bash
# 1. 检查模板文件
ls -la backend/excel_files/templates/

# 2. 检查目录权限
chmod 755 backend/excel_files/
chmod 755 backend/excel_files/generated/

# 3. 检查磁盘空间
df -h

# 4. 查看错误日志
tail -f logs/backend.log
```

#### Excel文件下载失败
**症状**: 点击下载按钮无响应或下载失败
**解决方案**:
```bash
# 1. 检查文件是否存在
ls -la backend/excel_files/generated/

# 2. 检查nginx配置
sudo nginx -t
grep -A 10 "excel_files" /etc/nginx/conf.d/export_excel.conf

# 3. 检查文件权限
chmod 644 backend/excel_files/generated/*.xlsx
```

### 🔐 权限问题

#### 文件权限错误
**症状**: Permission denied 错误
**解决方案**:
```bash
# 1. 检查文件所有者
ls -la backend/excel_files/

# 2. 修改文件权限
sudo chown -R $USER:$USER backend/excel_files/
chmod -R 755 backend/excel_files/

# 3. 检查SELinux状态（CentOS/RHEL）
getenforce
sudo setsebool -P httpd_can_network_connect 1
```

#### 数据库权限不足
**症状**: Access denied for user
**解决方案**:
```sql
-- 1. 检查用户权限
SHOW GRANTS FOR 'junguangchen'@'%';

-- 2. 授予必要权限
GRANT ALL PRIVILEGES ON export_excel_*.* TO 'junguangchen'@'%';
FLUSH PRIVILEGES;

-- 3. 创建新用户（如果需要）
CREATE USER 'export_user'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON export_excel_*.* TO 'export_user'@'%';
```

## 🔍 诊断工具

### 系统诊断脚本
```bash
# 运行内置诊断脚本
cd backend
python diagnose.py
```

### 手动检查清单
```bash
# 1. 检查所有服务状态
sudo systemctl status mysql redis nginx

# 2. 检查端口占用
netstat -tlnp | grep -E "(3306|6379|5000|9999)"

# 3. 检查进程状态
ps aux | grep -E "(mysql|redis|nginx|python)"

# 4. 检查日志文件
tail -f /var/log/nginx/error.log
tail -f logs/backend.log
tail -f /var/log/mysql/error.log
```

### 网络连接测试
```bash
# 1. 测试本地连接
curl http://localhost:9999
curl http://localhost:5000/health

# 2. 测试数据库连接
mysql -h ************ -u junguangchen -p -e "SELECT 1"

# 3. 测试Redis连接
redis-cli -h ************ -p 6379 -a 1qaz@WSX ping
```

## 📝 日志分析

### 后端日志
```bash
# 查看实时日志
tail -f logs/backend.log

# 搜索错误信息
grep -i error logs/backend.log
grep -i exception logs/backend.log

# 查看特定时间段日志
grep "2024-01-01" logs/backend.log
```

### Nginx日志
```bash
# 查看访问日志
tail -f /var/log/nginx/access.log

# 查看错误日志
tail -f /var/log/nginx/error.log

# 分析API请求
grep "/api/" /var/log/nginx/access.log
```

### 数据库日志
```bash
# MySQL错误日志
tail -f /var/log/mysql/error.log

# 慢查询日志
tail -f /var/log/mysql/slow.log
```

## 🚀 性能优化

### 数据库优化
```sql
-- 查看慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 分析表结构
EXPLAIN SELECT * FROM excel_file WHERE created_at > '2024-01-01';

-- 添加索引
CREATE INDEX idx_created_at ON excel_file(created_at);
```

### Redis优化
```bash
# 查看Redis内存使用
redis-cli -h ************ -p 6379 -a 1qaz@WSX info memory

# 清理过期键
redis-cli -h ************ -p 6379 -a 1qaz@WSX flushdb
```

### 应用优化
```python
# 启用调试模式查看详细错误
export FLASK_DEBUG=1
export FLASK_ENV=development

# 使用性能分析工具
pip install flask-profiler
```

## 🔄 恢复操作

### 数据库恢复
```bash
# 1. 备份当前数据
mysqldump -h ************ -u junguangchen -p export_excel_prod > backup_$(date +%Y%m%d).sql

# 2. 恢复数据库
mysql -h ************ -u junguangchen -p export_excel_prod < backup_20240101.sql

# 3. 重新初始化数据库
cd backend
python init_db.py --env production
```

### 应用重启
```bash
# 1. 停止所有服务
sudo systemctl stop nginx
pkill -f "python.*run.py"

# 2. 重新部署
./deploy.sh --auto-start

# 3. 验证服务状态
curl http://localhost:9999/api/health
```

## 📞 获取帮助

### 日志收集
在寻求帮助时，请收集以下信息：
```bash
# 1. 系统信息
uname -a
cat /etc/os-release

# 2. 服务状态
sudo systemctl status mysql redis nginx

# 3. 错误日志
tail -100 logs/backend.log
tail -100 /var/log/nginx/error.log

# 4. 配置文件
cat backend/config.py
cat /etc/nginx/conf.d/export_excel.conf
```

### 联系方式
- 技术支持邮箱: <EMAIL>
- 问题反馈: GitHub Issues
- 文档更新: 提交Pull Request

## 📚 相关文档

- [数据库配置](database-setup.md)
- [API文档](api-documentation.md)
- [配置说明](configuration.md)
- [部署指南](../deployment/environment-setup.md)
