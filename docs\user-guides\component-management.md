# 🧩 组件管理指南

## 📋 概述

组件管理是系统的核心功能之一，允许管理员配置和管理各种表单类型的组件信息。所有组件配置都存储在数据库中，支持动态配置和实时生效。

## 🎯 组件分类体系

### 安全测评组件分类
- **基础组件** (basic): 基础设施和核心服务
- **数据库组件** (database): 各类数据库系统
- **工具组件** (tools): 开发和运维工具
- **服务组件** (services): 应用服务和中间件
- **引擎组件** (engines): 搜索、计算等引擎
- **前端组件** (frontend): 前端框架和库
- **后端组件** (backend): 后端服务和框架

### 安全监测组件分类
- **监测工具** (monitoring): 监控和告警工具
- **数据采集** (collection): 数据收集组件
- **分析引擎** (analysis): 数据分析和处理

### 应用加固组件分类
- **加固工具** (hardening): 应用加固工具
- **防护组件** (protection): 安全防护组件

## 🔧 组件管理界面

### 访问组件管理
1. 登录系统后，点击导航栏的"组件管理"
2. 选择要管理的表单类型
3. 查看对应的组件分类和组件列表

### 界面布局
- **顶部**: 表单类型选择器
- **左侧**: 组件分类列表
- **右侧**: 组件详情和操作区域
- **底部**: 批量操作工具栏

## 📂 分类管理

### 查看分类
1. 在页面顶部选择表单类型
2. 左侧会显示该表单类型的所有分类
3. 每个分类显示图标、名称和组件数量

### 创建分类
1. 点击"新增分类"按钮
2. 填写分类信息：
   - **分类键名**: 唯一标识符（英文）
   - **显示名称**: 中文显示名称
   - **图标**: 选择Bootstrap图标
   - **颜色**: 选择分类颜色主题
   - **适用表单**: 勾选适用的表单类型
   - **排序权重**: 控制显示顺序

### 编辑分类
1. 在分类卡片中点击"编辑"按钮
2. 修改分类信息
3. 点击"保存"确认修改

### 删除分类
1. 在分类卡片中点击"删除"按钮
2. 确认删除操作
3. ⚠️ 注意：删除分类会同时删除该分类下的所有组件

## 🧩 组件管理

### 查看组件
1. 展开分类卡片
2. 查看该分类下的所有组件
3. 组件卡片显示：
   - 组件名称
   - 默认端口
   - 描述信息
   - 启用状态

### 添加组件
1. 点击分类中的"添加组件"按钮
2. 填写组件信息：
   - **组件名称**: 组件的显示名称
   - **分类**: 选择所属分类
   - **默认端口**: 组件的默认端口号
   - **描述**: 组件的详细描述
   - **适用表单**: 选择适用的表单类型

### 编辑组件
1. 在组件卡片的下拉菜单中选择"编辑"
2. 修改组件信息
3. 点击"保存"确认修改

### 启用/停用组件
1. 点击组件卡片上的小绿点
2. 绿点亮起表示启用，灰色表示停用
3. 停用的组件不会在表单中显示

### 删除组件
1. 在组件卡片的下拉菜单中选择"删除"
2. 确认删除操作
3. 组件将被永久删除

## 📊 批量操作

### 表格视图
1. 点击"切换到表格视图"按钮
2. 以表格形式查看所有组件
3. 支持排序、筛选和搜索

### 批量编辑
1. 在表格视图中勾选多个组件
2. 点击"批量编辑"按钮
3. 统一修改选中组件的属性

### 批量启用/停用
1. 勾选要操作的组件
2. 点击"批量启用"或"批量停用"
3. 确认操作

### 批量删除
1. 勾选要删除的组件
2. 点击"批量删除"按钮
3. 确认删除操作

## 🔍 搜索和筛选

### 搜索功能
- 在搜索框中输入关键词
- 支持按组件名称、描述搜索
- 实时搜索，无需点击搜索按钮

### 筛选功能
- **按分类筛选**: 选择特定分类查看组件
- **按状态筛选**: 查看启用/停用的组件
- **按表单类型筛选**: 查看特定表单类型的组件

## 📈 组件统计

### 统计信息
- 总组件数量
- 各分类组件数量
- 启用/停用组件数量
- 各表单类型组件分布

### 统计图表
- 分类分布饼图
- 状态分布柱状图
- 表单类型分布图

## 🔧 高级功能

### 组件导入导出
1. **导出组件**: 将组件配置导出为JSON文件
2. **导入组件**: 从JSON文件导入组件配置
3. **模板下载**: 下载组件配置模板

### 组件复制
1. 选择要复制的组件
2. 点击"复制"按钮
3. 修改组件名称和其他信息
4. 保存新组件

### 组件排序
1. 在分类中拖拽组件卡片
2. 调整组件显示顺序
3. 自动保存排序结果

## 🔒 权限控制

### 操作权限
- **查看权限**: 所有用户可查看组件信息
- **编辑权限**: 管理员可编辑组件配置
- **删除权限**: 超级管理员可删除组件

### 分类权限
- **分类管理**: 仅超级管理员可管理分类
- **组件管理**: 管理员可管理组件
- **批量操作**: 需要相应的操作权限

## 📝 最佳实践

### 命名规范
- 组件名称使用中文，简洁明了
- 分类键名使用英文，遵循驼峰命名
- 描述信息详细准确

### 分类设计
- 按功能逻辑分类，避免重叠
- 控制分类数量，保持简洁
- 考虑扩展性，预留发展空间

### 组件配置
- 设置准确的默认端口
- 提供详细的组件描述
- 及时更新组件状态

## 🚨 注意事项

### 数据安全
- 定期备份组件配置数据
- 重要操作前确认无误
- 避免误删重要组件

### 性能优化
- 控制组件数量，避免过多
- 及时清理无用组件
- 合理设置缓存策略

### 兼容性
- 新增组件时考虑向后兼容
- 修改分类时注意影响范围
- 删除操作需谨慎评估

## 📚 相关文档

- [表单管理指南](form-management.md)
- [Excel模板使用](excel-templates.md)
- [系统管理功能](system-administration.md)
- [API接口文档](../backend/api-documentation.md)
